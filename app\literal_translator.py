import time
import logging
from openai import OpenAI, APIStatusError, APITimeoutError
from .config import LITERAL_TRANSLATOR_MAX_RETRIES, OPENAI_REQUEST_TIMEOUT_SECONDS

logger = logging.getLogger(__name__)

def translate_text_literal(api_client: OpenAI, model_name: str, text_to_translate: str, retry_count: int = 0) -> str:
    try:
        response_cn = api_client.chat.completions.create(
            model=model_name, 
            messages=[
                {"role": "system", "content": "你是一个专业的翻译助手，请将文本直译为中文，保持原意不变。"},
                {"role": "user", "content": text_to_translate}
            ], 
            temperature=0.3, 
            stream=True,
            timeout=OPENAI_REQUEST_TIMEOUT_SECONDS
        )
        chinese_chunks = []
        for chunk in response_cn:
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                chinese_chunks.append(chunk.choices[0].delta.content)
        chinese = ''.join(chinese_chunks).strip()

        response_en = api_client.chat.completions.create(
            model=model_name, 
            messages=[
                {"role": "system", "content": "You are a professional translation assistant. Please translate the text literally into English, keeping the original meaning unchanged."},
                {"role": "user", "content": text_to_translate}
            ], 
            temperature=0.3, 
            stream=True,
            timeout=OPENAI_REQUEST_TIMEOUT_SECONDS
        )
        english_chunks = []
        for chunk in response_en:
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                english_chunks.append(chunk.choices[0].delta.content)
        english = ''.join(english_chunks).strip()
        
        return f"【中文直译】：{chinese}\n\n【英文直译】：{english}"
    except APITimeoutError as e:
        if retry_count < LITERAL_TRANSLATOR_MAX_RETRIES:
            logger.warning(f"Literal Translator: API timeout for '{text_to_translate[:30]}...' (Attempt {retry_count + 1}/{LITERAL_TRANSLATOR_MAX_RETRIES + 1}). Retrying in {1 + retry_count}s. Error: {e}")
            time.sleep(1 + retry_count)
            return translate_text_literal(api_client, model_name, text_to_translate, retry_count + 1)
        else:
            error_msg = f"中英直译错误: API调用超时 (已重试 {LITERAL_TRANSLATOR_MAX_RETRIES} 次) - {str(e)[:100]}"
            logger.error(f"Literal Translator: {error_msg} for '{text_to_translate[:30]}...'")
            return error_msg
    except APIStatusError as e:
        status_code = e.status_code
        error_detail = f"HTTP {status_code}: {e.message}"
        log_message = f"Literal Translator: APIStatusError for '{text_to_translate[:30]}...' (Attempt {retry_count + 1}). Details: {error_detail}"
        
        if status_code == 401:
            error_detail = f"HTTP {status_code} (认证失败): 请检查API密钥。 {e.message}"
        elif status_code == 429:
            error_detail = f"HTTP {status_code} (请求超限): 请稍后重试。 {e.message}"
            if retry_count < LITERAL_TRANSLATOR_MAX_RETRIES:
                logger.warning(f"Literal Translator: API rate limit for '{text_to_translate[:30]}...' (Attempt {retry_count + 1}/{LITERAL_TRANSLATOR_MAX_RETRIES + 1}). Retrying in {1 + retry_count}s. Error: {e}")
                time.sleep(1 + retry_count)
                return translate_text_literal(api_client, model_name, text_to_translate, retry_count + 1)
        elif status_code >= 500:
            error_detail = f"HTTP {status_code} (服务器错误): 服务暂时不可用。 {e.message}"
        
        logger.error(log_message)
        error_msg = f"中英直译错误: API调用失败 ({error_detail[:150]}) (已重试 {retry_count} 次)"
        return error_msg
    except Exception as e:
        logger.error(f"Literal Translator: Unexpected error for '{text_to_translate[:30]}...' (Attempt {retry_count + 1}). Error: {e}", exc_info=True)
        if retry_count < LITERAL_TRANSLATOR_MAX_RETRIES:
            time.sleep(1 + retry_count)
            return translate_text_literal(api_client, model_name, text_to_translate, retry_count + 1)
        else: 
            error_msg = f"中英直译错误: API调用失败 (已重试 {LITERAL_TRANSLATOR_MAX_RETRIES} 次) - {str(e)[:100]}"
            logger.error(f"Literal Translator: Max retries reached for '{text_to_translate[:30]}...'. Final error: {e}")
            return error_msg
