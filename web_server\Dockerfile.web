# web_server/Dockerfile.web
FROM python:3.9-slim

# Set working directory
WORKDIR /usr/src/web_app

# Install system dependencies that might be needed (e.g., for timezone, locales)
# RUN apt-get update && apt-get install -y --no-install-recommends t<PERSON>ta && rm -rf /var/lib/apt/lists/*

# Copy web server requirements first to leverage Docker cache
COPY ./web_server/web_requirements.txt .

# Install Python dependencies for the web server
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r web_requirements.txt

# Install Docker CLI
# RUN apt-get update && apt-get install -y docker.io --no-install-recommends && rm -rf /var/lib/apt/lists/*
# Try docker-ce-cli if docker.io is not found or preferred
RUN apt-get update && \
    apt-get install -y --no-install-recommends apt-transport-https ca-certificates curl gnupg lsb-release && \
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg && \
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian \
      $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y --no-install-recommends docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# Copy the rest of the web server application code
# This assumes uvicorn is run from the project root later or paths are adjusted
COPY ./web_server /usr/src/web_app/web_server

# Copy the 'app' directory for direct anwendung logic access from web_server
COPY ./app /usr/src/web_app/app

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application using Uvicorn
# The command assumes you will run it from the directory where main_web.py is located
# Adjust the path to main_web:app if your project structure is different inside the container
CMD ["uvicorn", "web_server.main_web:app", "--host", "0.0.0.0", "--port", "8000"] 