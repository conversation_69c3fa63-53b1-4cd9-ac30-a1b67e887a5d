import pandas as pd
import re
from collections import Counter
import logging

logger = logging.getLogger(__name__)

def extract_quality_ratings_scorer(analysis_text):
    if not analysis_text or not isinstance(analysis_text, str): 
        return []
    
    pattern = r'【质量】：\s*([^;；\n]+)'
    matches = re.findall(pattern, analysis_text)
    
    valid_ratings = ['不合格', '合格', '良好', '优秀']
    extracted_ratings = []
    for match in matches:
        cleaned_match = match.strip()
        if cleaned_match in valid_ratings:
            extracted_ratings.append(cleaned_match)
    return extracted_ratings

def determine_final_rating_scorer(ratings):
    if not ratings: return '无评价'
    if '不合格' in ratings: return '不合格'
    counter = Counter(ratings)
    priority = {'优秀': 3, '良好': 2, '合格': 1, '无评价': 0}
    sorted_ratings = sorted(counter.items(), key=lambda x: (x[1], priority.get(x[0], 0)), reverse=True)
    return sorted_ratings[0][0] if sorted_ratings else '无评价'

def run_translation_scoring_core(input_df, analysis_column_name="Analysis"):
    if not isinstance(input_df, pd.DataFrame) or input_df.empty:
        logger.info("Scorer: 输入DataFrame为空或非DataFrame对象。")
        return input_df, {}
    if analysis_column_name not in input_df.columns:
        logger.error(f"Scorer: 输入DataFrame缺少指定的分析列 '{analysis_column_name}'。")
        return input_df, {} 
    
    scored_df = input_df.copy()
    output_rating_column_name = "评价"
    if output_rating_column_name not in scored_df.columns:
        scored_df[output_rating_column_name] = ""
    
    total_rows = len(scored_df)
    logger.info(f"开始AI翻译评分，共 {total_rows} 条记录...")
    
    for idx, row in scored_df.iterrows():
        analysis_text = row[analysis_column_name]
        ratings = extract_quality_ratings_scorer(analysis_text)
        final_rating = determine_final_rating_scorer(ratings)
        scored_df.loc[idx, output_rating_column_name] = final_rating
        if (idx + 1) % max(1, total_rows // 10) == 0 or (idx + 1) == total_rows:
            logger.info(f"AI翻译评分中... {idx+1}/{total_rows} ({(idx + 1)/total_rows:.0%})")
            
    logger.info("AI翻译评分完成！")
    
    rating_stats = {}
    if output_rating_column_name in scored_df.columns:
        rating_counts = scored_df[output_rating_column_name].value_counts().to_dict()
        logger.info(f"AI翻译评分结果统计: {rating_counts}")
        rating_stats = rating_counts
        
    return scored_df, rating_stats
