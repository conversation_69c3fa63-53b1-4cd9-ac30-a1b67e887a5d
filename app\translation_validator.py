import pandas as pd
import json
import re
import time
import logging
import os
from openai import OpenAI, APIStatusError,APITimeoutError
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import httpx

from .config import (
    MAX_API_RETRIES_VALIDATOR, API_RETRY_BASE_DELAY_SECONDS_VALIDATOR,
    LITERAL_TRANSLATOR_MODEL, DECOMPOSITION_MODEL,
    LITERAL_TRANSLATOR_BASE_URL, DECOMPOSITION_BASE_URL,
    # Constants for OpenAI client timeouts
    OPENAI_CONNECT_TIMEOUT_SECONDS, 
    OPENAI_REQUEST_TIMEOUT_SECONDS
)
from .literal_translator import translate_text_literal
from .translation_decomposer import explain_translation_text_decomposition_adapted

logger = logging.getLogger(__name__)

def parse_and_categorize_analysis_validator(analysis_string):
    if not analysis_string or not isinstance(analysis_string, str):
        return "", False, 0

    if analysis_string.startswith("分析错误:") or analysis_string.startswith("任务执行错误:"):
        return analysis_string, True, -1 

    line_pattern = re.compile(
        r"^\s*(?:\d+\.?\s*)?(.*?)\s*[:：]\s*(?:【质量】|【评级】)\s*[:：]\s*(.*?)\s*[;；]\s*(?:【原因】\s*[:：]\s*)?(.*)$"
    )
    unqualified_list = []
    qualified_list = []
    good_list = []
    excellent_list = []
    has_unqualified = False
    highest_level = -1 

    for line in analysis_string.splitlines():
        stripped_line = line.strip()
        if not stripped_line:
            continue
        if stripped_line.startswith(("（注：", "注：", "---", "*注：", "※未涉及维度说明：", "- 文化隐喻", "- 参考服从性")):
            continue

        match = line_pattern.match(stripped_line)
        if match:
            dimension_text_from_regex, quality_raw, reason = match.groups()
            dimension_text_stripped = dimension_text_from_regex.strip()
            dimension_no_num_prefix = re.sub(r"^\d+\.?\s*", "", dimension_text_stripped)
            qwen_style_prefix = "【维度名称】："
            if dimension_no_num_prefix.startswith(qwen_style_prefix):
                dimension = dimension_no_num_prefix[len(qwen_style_prefix):].strip()
            else:
                dimension = dimension_no_num_prefix
            
            reason = reason.strip()
            quality_stripped = quality_raw.strip()
            quality = re.sub(r"[:：].*$", "", quality_stripped).strip().replace("【", "").replace("】", "")

            detail_line = f"{dimension}：【质量】：{quality}；【原因】：{reason}"

            valid_ratings = ["合格", "良好", "优秀", "不合格"]
            skip_ratings = [
                "不适用", "N/A", "跳过", "-", "/", "未评价",
                "该维度跳过", "无", "未提供信息", "未提供跳过",
                "跳过此维度评价"
            ]

            if quality in skip_ratings:
                continue 
            elif quality not in valid_ratings:
                logger.debug(f"Validator Parse: Skipped line due to unrecognized quality '{quality}' in line: {stripped_line}")
                continue 

            if quality == "不合格":
                unqualified_list.append(detail_line)
                has_unqualified = True
                highest_level = max(highest_level, 0)
            elif quality == "合格":
                qualified_list.append(detail_line)
                highest_level = max(highest_level, 1)
            elif quality == "良好":
                good_list.append(detail_line)
                highest_level = max(highest_level, 2)
            elif quality == "优秀":
                excellent_list.append(detail_line)
                highest_level = max(highest_level, 3)

    if not unqualified_list and not qualified_list and not good_list and not excellent_list:
        if analysis_string and highest_level == -1:
             logger.warning(f"Validator Parse: Analysis string provided but no valid categories extracted. Original: {analysis_string[:200]}...")
        return "", False, 0

    all_details_parts = []
    if unqualified_list:
        all_details_parts.append("\n".join(unqualified_list))
    other_parts_list = qualified_list + good_list + excellent_list
    if other_parts_list:
        all_details_parts.append("\n".join(other_parts_list))
    separator = "\n\n" if unqualified_list and other_parts_list else "\n"
    final_analysis_string = separator.join(filter(None, all_details_parts))

    return final_analysis_string, has_unqualified, highest_level

def _prepare_excel_row_data_validator(key, analysis_data):
    orig_text = analysis_data.get("original_text", "")
    trans_text = analysis_data.get("translated_text", "")
    
    std_analysis_tuple = analysis_data.get("standard_analysis")
    rhy_analysis_tuple = analysis_data.get("rhyme_analysis") 
    
    analysis_parts_for_cell = []
    overall_has_unqualified = False
    sort_level_from_standard = -1
    std_text = ""
    rhyme_text = ""
    rhyme_is_unqualified = False

    if std_analysis_tuple and len(std_analysis_tuple) >= 3:
        std_text, has_unq, level = std_analysis_tuple
        if has_unq: overall_has_unqualified = True
        sort_level_from_standard = level
    elif std_analysis_tuple and isinstance(std_analysis_tuple, tuple) and len(std_analysis_tuple) > 0:
        std_text = str(std_analysis_tuple[0])
        logger.debug(f"Key {key}: Standard analysis tuple was incomplete or malformed: {std_analysis_tuple}")
        overall_has_unqualified = True
        sort_level_from_standard = -1
    else:
        std_text = "错误：标准分析缺失或格式错误"
        overall_has_unqualified = True 
        sort_level_from_standard = -1

    if rhy_analysis_tuple and len(rhy_analysis_tuple) >= 3:
        rhyme_text_raw, rhyme_has_unq, _ = rhy_analysis_tuple
        rhyme_text = "---押韵专项分析---\n" + rhyme_text_raw
        if rhyme_has_unq:
            rhyme_is_unqualified = True
            overall_has_unqualified = True 
    elif rhy_analysis_tuple and isinstance(rhy_analysis_tuple, tuple) and len(rhy_analysis_tuple) > 0:
        rhyme_text = "---押韵专项分析---\n" + str(rhy_analysis_tuple[0])
        logger.debug(f"Key {key}: Rhyme analysis tuple was incomplete or malformed: {rhy_analysis_tuple}")
        rhyme_is_unqualified = True 
        overall_has_unqualified = True
        
    if rhyme_is_unqualified and rhyme_text:
        analysis_parts_for_cell.append(rhyme_text)
    if std_text:
        analysis_parts_for_cell.append(std_text)
    if not rhyme_is_unqualified and rhyme_text:
         analysis_parts_for_cell.append(rhyme_text)
    
    separator = "\n\n" if std_text and rhyme_text and rhyme_text in analysis_parts_for_cell and std_text in analysis_parts_for_cell else "\n"
    final_combined_text = separator.join(filter(None, analysis_parts_for_cell))

    if overall_has_unqualified:
         final_excel_sort_level = -1 if sort_level_from_standard == -1 else 0
    else:
         final_excel_sort_level = sort_level_from_standard

    analysis_note = final_combined_text
    if overall_has_unqualified and not (final_combined_text.startswith("错误：") or final_combined_text.startswith("处理被用户停止")):
         analysis_note = "⚠️ " + final_combined_text
    
    literal_trans = analysis_data.get("literal_translation", "N/A")
    decomp_analysis = analysis_data.get("decomposition_analysis", "N/A")
        
    return {
        "Key": key,
        "Original": orig_text[:100] + "..." if len(orig_text) > 100 else orig_text,
        "Translated": trans_text[:100] + "..." if len(trans_text) > 100 else trans_text,
        "Analysis": analysis_note,
        "LiteralTranslation": literal_trans, 
        "Decomposition": decomp_analysis,   
        "HasUnqualified": overall_has_unqualified,
        "SortLevel": final_excel_sort_level
    }

def analyze_translation_validator(api_clients: dict, key: str, item_data: dict, prompt_to_use: str, task_type: str, model_to_use: str, validator_should_stop_event=None):
    """
    Analyzes a single translation item.
    Args:
        api_clients: Dict containing initialized 'qwen' and 'deepseek' OpenAI clients.
        key: Identifier for the item.
        item_data: Dict containing '原文' and '译文'.
        prompt_to_use: The prompt string for the API call.
        task_type: 'standard' or 'rhyme'.
        model_to_use: Name of the model for the API call.
        validator_should_stop_event: Optional threading.Event to signal stop.

    Returns:
        A tuple: (key, original_text, translated_text, analysis_result_str, status_type, status_detail)
        status_type can be 'success', 'error', 'stopped'.
    """
    
    original_text = item_data.get("原文", "")
    translated_text = item_data.get("译文", "")

    if validator_should_stop_event and validator_should_stop_event.is_set():
        logger.info(f"Task for key {key} ({task_type}) stopped by global stop event before API call.")
        return key, original_text, translated_text, "处理被用户停止", "stopped", "Stopped by user event"
    
    if not original_text or not translated_text:
        logger.warning(f"Task for key {key} ({task_type}): Original or translated text is empty. Skipping API call.")
        return key, original_text, translated_text, "原文或译文为空", "error", "Input Error: Empty text"
        
    extra_params = {}
    client_options = {
        "timeout": OPENAI_REQUEST_TIMEOUT_SECONDS,
    }

    selected_client = None
    if task_type == 'standard':
        selected_client = api_clients.get('qwen')
        # extra_params = {"extra_body": {"enable_thinking": False}} # Specific to some Qianfan setups, might not be general OpenAI
    elif task_type == 'rhyme':
        selected_client = api_clients.get('deepseek')
    else:
        logger.error(f"Task for key {key}: Unknown task_type '{task_type}'.")
        return key, original_text, translated_text, f"未知的任务类型: {task_type}", "error", "Config Error: Unknown task type"

    if selected_client is None:
        logger.error(f"Task for key {key} ({task_type}): API client for this type is not available or not initialized.")
        return key, original_text, translated_text, "API客户端未正确初始化", "error", "API Client Error"

    retries = 0
    while retries <= MAX_API_RETRIES_VALIDATOR:
        if validator_should_stop_event and validator_should_stop_event.is_set():
            logger.info(f"Task for key {key} ({task_type}) stopped by global stop event during retry loop (attempt {retries}).")
            return key, original_text, translated_text, "处理在重试期间被用户停止", "stopped", "Stopped by user event during retries"

        try:
            analysis_result_str = ""
            request_start_time = time.time()
            logger.info(f"Task {key} ({task_type}): Calling {model_to_use} (Attempt {retries + 1})...")

            # Standardized way to call, stream for 'standard', non-stream for 'rhyme' (as per original logic)
            if task_type == 'standard':
                response_stream = selected_client.chat.completions.create(
                    model=model_to_use, 
                    messages=[
                        {"role": "system", "content": "您是一位专业的翻译质量评估专家，精通多语言和文化背景。"}, 
                        {"role": "user", "content": prompt_to_use}
                    ],
                    temperature=0.5, 
                    stream=True, 
                    **extra_params, # Ensure extra_params is defined or empty dict
                    **client_options
                )
                collected_chunks = []
                for chunk in response_stream:
                    if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                        collected_chunks.append(chunk.choices[0].delta.content)
                analysis_result_str = "".join(collected_chunks)
            else: # rhyme or other types if non-streaming is default
                response = selected_client.chat.completions.create(
                    model=model_to_use, 
                    messages=[
                        {"role": "system", "content": "您是一位专业的翻译质量评估专家，精通多语言和文化背景。"}, 
                        {"role": "user", "content": prompt_to_use}
                    ],
                    temperature=0.5, 
                    **extra_params,
                    **client_options
                )
                if response.choices and response.choices[0].message:
                    analysis_result_str = response.choices[0].message.content
            
            request_duration = time.time() - request_start_time
            logger.info(f"Task {key} ({task_type}): {model_to_use} call completed in {request_duration:.2f}s. Length: {len(analysis_result_str)}")

            if not analysis_result_str or len(analysis_result_str) < 10: 
                logger.warning(f"Task {key} ({task_type}): API response too short or empty. Length: {len(analysis_result_str)}. Response: '{analysis_result_str[:50]}...'")
                # Potentially retry for short/empty responses as well, or return error directly
                # For now, let's treat it as an error to avoid infinite loops on consistently short problematic responses
                if retries < MAX_API_RETRIES_VALIDATOR: # Allow retries for this specific case
                     retries += 1
                     logger.info(f"Task {key} ({task_type}): Retrying due to short response ({retries}/{MAX_API_RETRIES_VALIDATOR + 1}).")
                     time.sleep(API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2 ** (retries -1)))
                     continue # Retry the API call
                return key, original_text, translated_text, "API返回内容过短或为空", "error", f"Short Response (Length: {len(analysis_result_str)}) after retries"
            
            return key, original_text, translated_text, analysis_result_str, "success", f"Success (Length: {len(analysis_result_str)})"

        except APITimeoutError as e:
            retries += 1
            logger.warning(f"Task {key} ({task_type}): API timeout (Attempt {retries}/{MAX_API_RETRIES_VALIDATOR + 1}). Error: {e}")
            if retries <= MAX_API_RETRIES_VALIDATOR:
                delay = API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2 ** (retries -1))
                time.sleep(delay)
                # Check stop event again before continuing to next retry
                if validator_should_stop_event and validator_should_stop_event.is_set():
                    logger.info(f"Task {key} ({task_type}) stopped by global stop event during timeout retry.")
                    return key, original_text, translated_text, f"处理在重试等待期间被用户停止 (超时错误: {e})", "stopped", "Stopped during retry for timeout"
                continue
            else:
                final_error_msg = f"分析错误: API调用连续超时 {retries} 次. {e}"
                logger.error(f"Task {key} ({task_type}): {final_error_msg}")
                return key, original_text, translated_text, final_error_msg, "error", "API Timeout Error after retries"

        except APIStatusError as e:
            status_code = e.status_code
            retries += 1 # Count this as a retry attempt for APIStatusErrors as well
            log_message = f"Task {key} ({task_type}): API Error (HTTP {status_code}, Attempt {retries}/{MAX_API_RETRIES_VALIDATOR + 1}). Message: {e.message}"
            logger.warning(log_message)

            specific_error_info = f"HTTP状态码: {status_code}. "
            if status_code == 400: specific_error_info += "请求无效。"
            elif status_code == 401: specific_error_info += "认证失败。"
            elif status_code == 403: specific_error_info += "禁止访问。"
            elif status_code == 404: specific_error_info += "未找到。"
            elif status_code == 429:
                specific_error_info += "请求过于频繁或超出配额。"
                if retries <= MAX_API_RETRIES_VALIDATOR:
                    retry_after_seconds_str = e.response.headers.get("Retry-After")
                    delay = API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2 ** (retries -1))
                    if retry_after_seconds_str:
                        try: delay = max(delay, float(retry_after_seconds_str))
                        except ValueError: pass 
                    logger.info(f"Task {key} ({task_type}): API rate limit. Retrying in {delay:.1f}s.")
                    time.sleep(delay)
                    if validator_should_stop_event and validator_should_stop_event.is_set():
                         logger.info(f"Task {key} ({task_type}) stopped by global stop event during 429 retry.")
                         return key, original_text, translated_text, f"处理在重试等待期间被用户停止 (HTTP 429: {e.message})", "stopped", "Stopped during retry for 429"
                    continue
            elif status_code >= 500: specific_error_info += "服务器内部错误。"
            else: specific_error_info += f"客户端错误: {e.message}"

            if retries > MAX_API_RETRIES_VALIDATOR:
                final_error_msg = f"分析错误: API错误 (HTTP {status_code}) 达到最大重试次数. {e.message}"
                logger.error(f"Task {key} ({task_type}): {final_error_msg}")
                return key, original_text, translated_text, final_error_msg, "error", f"API Error (Code: {status_code}) after retries"
            # For non-429 errors that are not retried in the original code for more than once (implicitly), we might fall through here
            # Adding a sleep for other retryable status codes if not 429 and retries not exhausted
            if status_code not in [400,401,403,404]: # Don't retry these usually
                 time.sleep(API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2**(retries-1)))
                 continue # continue to retry for other status codes if retries not exhausted
            
            # If it's a non-retryable status code or retries exhausted for specific codes not handled by 429 loop
            final_error_msg_status = f"分析错误 (Retries: {retries}, Type: {type(e).__name__}): {specific_error_info} {e.message}"
            logger.error(f"Task {key} ({task_type}): {final_error_msg_status}")
            return key, original_text, translated_text, final_error_msg_status, "error", f"API Error (Code: {status_code})"
            
        except Exception as e:
            retries +=1 # Count this as a retry attempt as well
            logger.error(f"Task {key} ({task_type}): Unexpected error (Attempt {retries}/{MAX_API_RETRIES_VALIDATOR + 1}). Error: {e}", exc_info=True)
            if retries <= MAX_API_RETRIES_VALIDATOR:
                delay = API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2 ** (retries-1))
                time.sleep(delay)
                if validator_should_stop_event and validator_should_stop_event.is_set():
                    logger.info(f"Task {key} ({task_type}) stopped by global stop event during unexpected error retry.")
                    return key, original_text, translated_text, f"处理在重试等待期间被用户停止 (错误: {e})", "stopped", "Stopped during retry for unexpected error"
                continue
            else:
                final_error_msg = f"任务执行错误 (已达最大重试次数 {retries}, Type: {type(e).__name__}): {str(e)}"
                logger.error(f"Task {key} ({task_type}): {final_error_msg}")
                return key, original_text, translated_text, final_error_msg, "error", f"Unexpected Error after retries ({type(e).__name__})"

    # Fallback if loop finishes (all retries exhausted)
    all_retries_failed_msg = f"分析错误: 所有 {MAX_API_RETRIES_VALIDATOR + 1} 次尝试均失败 (条目: {key}, 类型: {task_type})."
    logger.error(f"Task {key} ({task_type}): {all_retries_failed_msg}")
    return key, original_text, translated_text, all_retries_failed_msg, "error", "Max Retries Exhausted"

def run_validation_batch(
    tasks_to_run: list,
    qwen_model_name: str,
    deepseek_model_name: str,
    qwen_api_key: str,
    deepseek_api_key: str,
    qwen_base_url: str = None, 
    deepseek_base_url: str = None, 
    enable_literal_translation: bool = False,
    literal_translator_model_name: str = None, 
    literal_translator_base_url: str = None, 
    enable_decomposition: bool = False,
    decomposition_model_name: str = None, 
    decomposition_base_url: str = None, 
    max_workers: int = 5,
    validator_should_stop_event=None # Pass a threading.Event if external stop is needed
):
    logger.info(f"Starting validation batch with {len(tasks_to_run)} tasks.")
    logger.info(f"Qwen Model: {qwen_model_name}, DeepSeek Model: {deepseek_model_name}")
    logger.info(f"Literal Translation: {'Enabled' if enable_literal_translation else 'Disabled'}, Model: {literal_translator_model_name if enable_literal_translation else 'N/A'}")
    logger.info(f"Decomposition: {'Enabled' if enable_decomposition else 'Disabled'}, Model: {decomposition_model_name if enable_decomposition else 'N/A'}")

    api_clients = {}
    literal_translator_api_client = None
    decomposition_api_client = None
    
    try:
        # 创建不使用系统代理的 httpx 客户端
        # Python 中 None 表示不使用代理, 对于 httpx.Client，传递 proxies={} 或 proxies=None 都可以。
        # 为了更明确，可以传递一个空的 proxies 字典。
        # 或者，如果 httpx 默认在 proxies=None 时不查找环境变量，则 None 也可以。
        # 根据 httpx 文档，None 通常是正确的选择来禁用从环境中获取代理。
        no_proxy_httpx_client = httpx.Client(proxies=None)

        # 为 OpenAI 客户端明确传递这个 httpx 客户端
        api_clients['qwen'] = OpenAI(
            api_key=qwen_api_key, 
            base_url=qwen_base_url, 
            timeout=OPENAI_CONNECT_TIMEOUT_SECONDS,
            http_client=no_proxy_httpx_client # 修改点
        )
        api_clients['deepseek'] = OpenAI(
            api_key=deepseek_api_key, 
            base_url=deepseek_base_url, 
            timeout=OPENAI_CONNECT_TIMEOUT_SECONDS,
            http_client=no_proxy_httpx_client # 修改点
        )
        logger.info("Qwen and DeepSeek API clients initialized.")

        if enable_literal_translation and literal_translator_model_name and qwen_api_key: 
            actual_literal_base_url = literal_translator_base_url if literal_translator_base_url else qwen_base_url
            literal_translator_api_client = OpenAI(
                api_key=qwen_api_key, 
                base_url=actual_literal_base_url, 
                timeout=OPENAI_CONNECT_TIMEOUT_SECONDS,
                http_client=no_proxy_httpx_client # 修改点
            )
            logger.info(f"Literal Translator API client initialized (Model: {literal_translator_model_name}, Base URL: {actual_literal_base_url or 'Default OpenAI'}).")
        elif enable_literal_translation:
            logger.warning("Literal translation enabled, but model name or Qwen API key is missing. Skipping.")

        if enable_decomposition and decomposition_model_name and qwen_api_key: 
            actual_decomposition_base_url = decomposition_base_url if decomposition_base_url else qwen_base_url
            decomposition_api_client = OpenAI(
                api_key=qwen_api_key, 
                base_url=actual_decomposition_base_url,
                timeout=OPENAI_CONNECT_TIMEOUT_SECONDS,
                http_client=no_proxy_httpx_client # 修改点
            )
            logger.info(f"Decomposition API client initialized (Model: {decomposition_model_name}, Base URL: {actual_decomposition_base_url or 'Default OpenAI'}).")
        elif enable_decomposition:
            logger.warning("Decomposition enabled, but model name or Qwen API key is missing. Skipping.")

    except Exception as e:
        logger.error(f"Failed to initialize API clients: {e}", exc_info=True) # 更通用的错误消息
        return [] # Critical failure
    
    aggregated_results_data = defaultdict(lambda: {
        "original_text": None, "translated_text": None,
        "standard_analysis": None, "rhyme_analysis": None,
        "literal_translation": "中英直译未启用或跳过",
        "decomposition_analysis": "翻译拆解未启用或跳过"
    })

    completed_task_count = 0
    total_tasks_in_batch = len(tasks_to_run)
    if total_tasks_in_batch == 0:
        logger.info("No tasks to run in this batch.")
        return []

    with ThreadPoolExecutor(max_workers=min(max_workers, total_tasks_in_batch)) as executor:
        futures_map = {}
        for task_idx, task_details in enumerate(tasks_to_run):
            if validator_should_stop_event and validator_should_stop_event.is_set():
                logger.info(f"Stop event set. Halting submission of new tasks. Submitted {task_idx} tasks.")
                break # Stop submitting new tasks
            
            task_key = task_details.get('key')
            task_item_data = task_details.get('item')
            task_prompt = task_details.get('prompt')
            task_type = task_details.get('type') 

            if not all([task_key, task_item_data, task_prompt, task_type]):
                logger.warning(f"Skipping task at index {task_idx} due to missing key, item, prompt, or type. Details: {task_details}")
                completed_task_count +=1 # Count as processed for progress, though skipped
                continue
                    
            model_for_this_task = None
            if task_type == 'standard':
                model_for_this_task = qwen_model_name
            elif task_type == 'rhyme':
                model_for_this_task = deepseek_model_name
            else:
                logger.warning(f"Skipping task {task_key}: Unknown task type '{task_type}'.")
                aggregated_results_data[task_key]["original_text"] = task_item_data.get("原文", "")
                aggregated_results_data[task_key]["translated_text"] = task_item_data.get("译文", "")
                aggregated_results_data[task_key][f"{task_type}_analysis"] = (f"未知任务类型 '{task_type}'", True, -1)
                completed_task_count +=1
                continue

            future = executor.submit(
                analyze_translation_validator,
                api_clients,
                task_key,
                task_item_data,      
                task_prompt,
                task_type,
                model_for_this_task,
                validator_should_stop_event # Pass the event
            )
            futures_map[future] = (task_key, task_type, task_item_data) 
            
        logger.info(f"Submitted {len(futures_map)} tasks to executor. Waiting for completion...")
        processed_futures = 0
        for future in as_completed(futures_map):
            # Even if stop event is set, we process already submitted tasks
            original_key, original_task_type, original_item_data = futures_map[future]
            try:
                res_key, res_orig_text, res_trans_text, res_analysis_str, res_status, res_detail = future.result()

                current_data = aggregated_results_data[res_key]
                if current_data["original_text"] is None: current_data["original_text"] = res_orig_text or original_item_data.get("原文", "")
                if current_data["translated_text"] is None: current_data["translated_text"] = res_trans_text or original_item_data.get("译文", "")
                    
                if res_status == "error" or res_status == "stopped":
                    logger.error(f"Task {res_key} ({original_task_type}) {res_status}: {res_detail}. Analysis: {res_analysis_str}")
                    current_data[f"{original_task_type}_analysis"] = (res_analysis_str, True, -1)
                else:
                    logger.info(f"Task {res_key} ({original_task_type}) succeeded. Detail: {res_detail}")
                    current_data[f"{original_task_type}_analysis"] = parse_and_categorize_analysis_validator(res_analysis_str)
                        
                current_valid_translated_text = current_data["translated_text"]
                if not isinstance(current_valid_translated_text, str): current_valid_translated_text = ""

                if enable_literal_translation and literal_translator_api_client and current_valid_translated_text:
                    if not (validator_should_stop_event and validator_should_stop_event.is_set()):
                        logger.info(f"Task {res_key}: Performing literal translation...")
                        try:
                            lt_result = translate_text_literal(literal_translator_api_client, literal_translator_model_name, current_valid_translated_text)
                            current_data["literal_translation"] = lt_result
                            if lt_result.startswith("中英直译错误:"): logger.warning(f"Task {res_key}: Literal translation warning: {lt_result}")
                        except Exception as lt_exc:
                            err_msg = f"中英直译时发生意外错误: {lt_exc}"
                            logger.error(f"Task {res_key}: {err_msg}", exc_info=True)
                            current_data["literal_translation"] = err_msg
                    else:
                        current_data["literal_translation"] = "中英直译因停止信号跳过"
                elif enable_literal_translation and not current_valid_translated_text: current_data["literal_translation"] = "中英直译跳过 (无有效译文)"

                if enable_decomposition and decomposition_api_client and current_valid_translated_text:
                    if not (validator_should_stop_event and validator_should_stop_event.is_set()):
                        logger.info(f"Task {res_key}: Performing decomposition analysis...")
                        try:
                            dc_result = explain_translation_text_decomposition_adapted(decomposition_api_client, decomposition_model_name, current_valid_translated_text)
                            current_data["decomposition_analysis"] = dc_result
                            if dc_result.startswith("翻译拆解错误:"): logger.warning(f"Task {res_key}: Decomposition analysis warning: {dc_result}")
                        except Exception as dc_exc:
                            err_msg = f"翻译拆解时发生意外错误: {dc_exc}"
                            logger.error(f"Task {res_key}: {err_msg}", exc_info=True)
                            current_data["decomposition_analysis"] = err_msg
                    else:
                        current_data["decomposition_analysis"] = "翻译拆解因停止信号跳过"
                elif enable_decomposition and not current_valid_translated_text: current_data["decomposition_analysis"] = "翻译拆解跳过 (无有效译文)"
            except Exception as exc:
                logger.error(f"Error processing result for task {original_key} ({original_task_type}): {exc}", exc_info=True)
                current_data_exc = aggregated_results_data[original_key]
                if current_data_exc["original_text"] is None: current_data_exc["original_text"] = original_item_data.get("原文", "")
                if current_data_exc["translated_text"] is None: current_data_exc["translated_text"] = original_item_data.get("译文", "")
                current_data_exc[f"{original_task_type}_analysis"] = (f"批处理中主结果处理错误: {exc}", True, -1)
            finally:
                processed_futures += 1
                if processed_futures % 10 == 0 or processed_futures == len(futures_map):
                    logger.info(f"Validation progress: {processed_futures}/{len(futures_map)} submitted tasks processed.")
    
    final_processed_count = len(aggregated_results_data.keys())
    logger.info(f"Batch processing finished. Attempted to process or processed {final_processed_count}/{total_tasks_in_batch} unique items.")

    excel_rows = []
    if not aggregated_results_data and tasks_to_run:
        logger.warning("Validation batch ran but aggregated no results.")
    elif not tasks_to_run:
        logger.info("Validation batch had no tasks to run.")
    else:
        logger.info(f"Preparing {len(aggregated_results_data)} items for Excel output.")
        for key_res, data_for_key in aggregated_results_data.items():
            excel_rows.append(_prepare_excel_row_data_validator(key_res, data_for_key))
        
    return excel_rows
