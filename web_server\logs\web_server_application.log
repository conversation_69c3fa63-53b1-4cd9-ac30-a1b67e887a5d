2025-06-03 14:16:04,710 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 14:46:34,436 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: ed084753-160c-4371-b788-3a8208ca1548) to 'uploads/ed084753-160c-4371-b788-3a8208ca1548/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 14:46:34,607 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 14:50:49,384 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 1715dbff-11bc-4d80-bb6e-dc882ac8b7ce) to 'uploads/1715dbff-11bc-4d80-bb6e-dc882ac8b7ce/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 14:50:49,408 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:16:38,769 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:16:45,060 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 198a03d4-aea2-42a5-bcb0-4b5b2929e1eb) to 'uploads/198a03d4-aea2-42a5-bcb0-4b5b2929e1eb/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:16:45,207 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:24:43,718 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:24:52,325 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: ea2f8e08-6dda-47ec-91ec-e09103ab73e6) to 'uploads/ea2f8e08-6dda-47ec-91ec-e09103ab73e6/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:24:52,549 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:25:03,970 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:25:04,828 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:26:29,457 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:26:41,881 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 9bb8531b-cab0-4d9c-9e52-d790952b57b5) to 'uploads/9bb8531b-cab0-4d9c-9e52-d790952b57b5/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:26:42,013 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:27:19,054 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:27:19,827 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:30:05,836 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:30:14,432 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: bc45e762-7e75-487b-83a8-bb1956f703bc) to 'uploads/bc45e762-7e75-487b-83a8-bb1956f703bc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:30:14,567 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:30:18,570 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:552] - Initiating task 6961cca9-9981-4243-bd4b-871e6d4e082c with payload: {'file_id': 'bc45e762-7e75-487b-83a8-bb1956f703bc', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-03 15:30:18,589 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:562] - Task 6961cca9-9981-4243-bd4b-871e6d4e082c: Copied initial file from uploads/bc45e762-7e75-487b-83a8-bb1956f703bc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/6961cca9-9981-4243-bd4b-871e6d4e082c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-03 15:30:18,595 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:591] - Task 6961cca9-9981-4243-bd4b-871e6d4e082c fully initialized in task_statuses. Mode: professional
2025-06-03 15:30:18,596 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:596] - Task 6961cca9-9981-4243-bd4b-871e6d4e082c: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-03 15:30:18,598 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:552] - Initiating task 453f4f1e-c290-4f84-b2d7-618b2d830a32 with payload: {'file_id': 'bc45e762-7e75-487b-83a8-bb1956f703bc', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': None}}
2025-06-03 15:30:18,616 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:562] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Copied initial file from uploads/bc45e762-7e75-487b-83a8-bb1956f703bc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-03 15:30:18,620 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:591] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32 fully initialized in task_statuses. Mode: professional
2025-06-03 15:30:18,621 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:596] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-03 15:30:22,978 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'preprocess'.
2025-06-03 15:30:22,980 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:30:22,982 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'preprocess'
2025-06-03 15:30:22,983 - WebServerAppLogger - INFO - [main_web.execute_professional_step:307] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:30:22,986 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:30:23,036 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'preprocess' processing finished. Status: completed
2025-06-03 15:30:23,037 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'preprocess' ended with success: True
2025-06-03 15:30:23,037 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'preprocess' successful. Next step is 'richtext'.
2025-06-03 15:30:24,372 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'richtext'.
2025-06-03 15:30:24,373 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:30:24,375 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'richtext'
2025-06-03 15:30:24,378 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'richtext': Determined input file: 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/preprocessed_output.json'
2025-06-03 15:30:24,412 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'richtext' processing finished. Status: completed
2025-06-03 15:30:24,413 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'richtext' ended with success: True
2025-06-03 15:30:24,414 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'richtext' successful. Next step is 'ai_processing'.
2025-06-03 15:30:25,497 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'ai_processing'.
2025-06-03 15:30:25,498 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:30:25,500 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'ai_processing'
2025-06-03 15:30:25,503 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'ai_processing': Determined input file: 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/preprocessed_output.json'
2025-06-03 15:30:25,508 - WebServerAppLogger - INFO - [main_web.execute_professional_step:355] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32 'ai_processing': AI progress initialized: {'completed': 0, 'total': 17, 'error': None}
2025-06-03 15:31:37,262 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'ai_processing' processing finished. Status: completed
2025-06-03 15:31:37,263 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'ai_processing' ended with success: True
2025-06-03 15:31:37,264 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'ai_processing' successful. Next step is 'scoring'.
2025-06-03 15:31:37,271 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'scoring'.
2025-06-03 15:31:37,273 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:31:37,276 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:31:37,278 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:31:37,281 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'scoring'
2025-06-03 15:31:37,285 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'scoring': Determined input file: 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/ai_processing_report.xlsx'
2025-06-03 15:31:37,286 - WebServerAppLogger - INFO - [main_web.execute_professional_step:392] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32 'scoring': Input 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/ai_processing_report.xlsx', Output 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/final_scoring_report.xlsx'
2025-06-03 15:31:37,350 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'scoring' processing finished. Status: completed
2025-06-03 15:31:37,351 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'scoring' ended with success: True
2025-06-03 15:31:37,352 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:484] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: All professional steps completed successfully.
2025-06-03 15:31:37,355 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: completed). Task might be completed or failed.
2025-06-03 15:31:37,359 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: completed). Task might be completed or failed.
2025-06-03 15:31:37,373 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: c700ad6c-b827-4332-aa26-c960110210cc) to 'uploads/c700ad6c-b827-4332-aa26-c960110210cc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:31:37,397 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 06:55:44,161 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 06:56:03,161 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: ca5da6fc-d6de-4864-8957-3e82fde874a8) to 'uploads/ca5da6fc-d6de-4864-8957-3e82fde874a8/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 06:56:03,377 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 06:56:09,970 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:552] - Initiating task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c with payload: {'file_id': 'ca5da6fc-d6de-4864-8957-3e82fde874a8', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 06:56:09,986 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:562] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Copied initial file from uploads/ca5da6fc-d6de-4864-8957-3e82fde874a8/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 06:56:09,990 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:591] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c fully initialized in task_statuses. Mode: professional
2025-06-04 06:56:09,991 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:596] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 06:56:12,225 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Queuing professional step 'preprocess'.
2025-06-04 06:56:12,227 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Starting execution of professional step: 'preprocess'
2025-06-04 06:56:12,228 - WebServerAppLogger - INFO - [main_web.execute_professional_step:307] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 06:56:12,231 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 06:56:12,284 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'preprocess' processing finished. Status: completed
2025-06-04 06:56:12,285 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Updating flow. Step 'preprocess' ended with success: True
2025-06-04 06:56:12,285 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'preprocess' successful. Next step is 'richtext'.
2025-06-04 06:56:29,444 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Queuing professional step 'richtext'.
2025-06-04 06:56:29,445 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Starting execution of professional step: 'richtext'
2025-06-04 06:56:29,448 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'richtext': Determined input file: 'outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/preprocessed_output.json'
2025-06-04 06:56:29,483 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'richtext' processing finished. Status: completed
2025-06-04 06:56:29,483 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Updating flow. Step 'richtext' ended with success: True
2025-06-04 06:56:29,484 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'richtext' successful. Next step is 'ai_processing'.
2025-06-04 06:57:05,195 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Queuing professional step 'ai_processing'.
2025-06-04 06:57:05,196 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Starting execution of professional step: 'ai_processing'
2025-06-04 06:57:05,199 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'ai_processing': Determined input file: 'outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/preprocessed_output.json'
2025-06-04 06:57:05,203 - WebServerAppLogger - INFO - [main_web.execute_professional_step:355] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c 'ai_processing': AI progress initialized: {'completed': 0, 'total': 17, 'error': None}
