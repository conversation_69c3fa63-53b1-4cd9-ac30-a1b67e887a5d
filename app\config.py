import re

# --- 常量定义 ---
# API 配置
# TODO: 未来应考虑从环境变量读取 API_KEY，而不是硬编码
API_KEY = "sk-GdEnMVpyEBgZtx0JAb9d57B7BeEc4c25B5Fb2e1eD043Fa16"  # 固定的API key

# 评分选项
RATING_OPTIONS = ["优秀", "良好", "合格", "不合格"]

# --- FILE_NAME_MAPPING_PREPROCESS ---
FILE_NAME_MAPPING_PREPROCESS = {
    "BossIterationsDesc": "角色名相关内容翻译",
    "BossRush": "BossRush版本的UI相关内容翻译",
    "BossRushDebuffDesc": "BossRush版本的debuff相关内容翻译",
    "Config": "地图标记相关内容翻译",
    "EquipDesc": "游戏装备相关内容翻译",
    "FUStBossRushConfigDesc": "怪物等级相关内容翻译",
    "FUStDialogueDesc": "游戏台词相关内容翻译",
    "FUStGuideNodeDesc": "游戏新手指导相关内容翻译",
    "FUStUIWordDesc": "游戏UI相关内容翻译",
    "InteractionFuncDesc": "游戏土地庙（复活点）功能相关内容翻译",
    "ItemDesc": "游戏道具物品相关内容翻译",
    "Loading": "游戏加载界面提示语相关内容翻译",
    "Map": "游戏地图界面操控相关内容翻译",
    "Museum": "游戏曲库相关内容翻译",
    "UISettingConfigDesc": "游戏设置相关内容翻译",
    "UnitBattleInfoExtendDesc": "游戏角色名相关内容翻译",
    "FUStTalentDisplayDesc": "游戏内天赋技能相关内容翻译",
    "EquipFaBaoAttrDesc": "游戏内法宝相关内容翻译",
    "FUStSuitDesc": "游戏套装相关内容翻译",
    "HuluDesc": "游戏内道具相关内容翻译",
    "WineDesc": "游戏内道具相关内容翻译",
    "LoadingTipsDesc": "游戏内加载界面文本翻译",
    "ChapterDesc": "游戏章节名称翻译",
    "FUStRebirthPointDesc": "游戏内复活点名称翻译",
    "FUStInteractionMappingDesc": "游戏内交互提示翻译",
    "TakePhotoCustomSettingDesc": "游戏内照相模式相关内容翻译",
    "FUStRebirthAreaDesc": "游戏内地名翻译",
    "AchievementDesc": "游戏成就相关翻译",
    "PS5ActivityTaskDesc": "游戏成就相关翻译",
    "SceneMonsterNameplateDesc": "游戏内地名和敌人名称翻译",
    "TravelNotes": "游戏UI相关内容翻译",
    "Setting": "游戏设置相关内容翻译",
    "Comm": "游戏UI相关内容翻译",
    "Btn": "游戏内交互提示翻译",
    "Award": "游戏UI相关内容翻译",
    "Drop_M": "游戏UI相关内容翻译",
    "StartGame": "游戏UI相关内容翻译",
    "Item": "游戏UI相关内容翻译",
    "Medicine": "游戏UI相关内容翻译",
    "Role": "游戏UI相关内容翻译",
    "EquipBuild": "游戏内道具相关内容翻译",
    "Farm": "游戏UI相关内容翻译",
    "WeaponBuild": "游戏内道具相关内容翻译",
    "Guide": "游戏UI相关内容翻译",
    "Genqi": "游戏UI相关内容翻译",
    "Drop": "游戏UI相关内容翻译",
    "BattleV2": "游戏UI相关内容翻译",
    "Input": "游戏UI相关内容翻译",
    "PlayGo": "游戏UI相关内容翻译",
    "Shop": "游戏UI相关内容翻译",
    "Chapter": "游戏UI相关内容翻译", # Note: Duplicate "Chapter" key, last one wins. Check if intentional.
    "Marker": "游戏UI相关内容翻译",
    "LearnSpell": "游戏UI相关内容翻译",
    "InitSetting": "游戏设置相关内容翻译",
    "Detail": "游戏UI相关内容翻译",
    "Login": "游戏UI相关内容翻译",
    "PhotoMode": "游戏内照相模式相关内容翻译",
    "Interact": "游戏UI相关内容翻译",
    "Notice": "游戏UI相关内容翻译",
    "SoulSkillCollect": "游戏UI相关内容翻译",
    "FUStDefeatSlowTimeConfigDesc": "游戏内敌人名称翻译",
    "CardDesc": "游戏内图鉴相关翻译",
    "SoakingWine": "游戏内道具相关内容翻译",
    "SoulSkillDesc": "游戏内天赋技能相关内容翻译",
    "FUStInteractiveUnitCommDesc": "游戏内角色名称翻译",
    "LevelDesc": "游戏章节名称翻译",
    "BenchMark": "游戏性能测试工具相关翻译",
    "EditionAward": "游戏UI相关内容翻译",
    "Story": "游戏UI相关内容翻译",
    "MeditationPointDesc": "游戏内地名翻译",
    "TransInputUITipsDesc": "游戏出招操作提示翻译",
    "CostItem": "游戏UI相关内容翻译",
    "MuseumBtn": "游戏曲库相关内容翻译",
    "MuseumMVDesc": "游戏曲库相关内容翻译",
    "PS5ActivityDesc": "游戏成就相关翻译",
    "CommonErrorUITipsDesc": "游戏报错提示翻译",
    "LinkBloodDesc": "游戏内敌人名称翻译",
    "SoundTrackDesc": "游戏曲库相关内容翻译",
    "MapAtlasConfigDesc": "游戏地图相关翻译"
}

# --- EXPECTED_COLUMNS_CONFIG_PREPROCESS ---
EXPECTED_COLUMNS_CONFIG_PREPROCESS = {
    "record_id": {"default_name": "Record ID", "description": "记录ID (Record ID)", "is_core": True},
    "file_name": {"default_name": "文件名 File Name", "description": "文件名 (File Name)", "is_core": True},
    "source_text": {"default_name": "English (United Kingdom)", "description": "原文 (English)", "is_core": True},
    "translation_text": {"default_name": "Translation", "description": "译文", "is_core": True},
    "speaker": {"default_name": "说话人 Speaker", "description": "说话人", "is_core": False},
    "comments": {"default_name": "EN Source Comments", "description": "英文源注释", "is_core": False},
    "path": {"default_name": "路径 Path", "description": "路径", "is_core": False},
    "scenario": {"default_name": "Scenario", "description": "场景", "is_core": False},
    "char_intro": {"default_name": "角色介绍 Character Intro", "description": "角色介绍", "is_core": False},
    "char_style": {"default_name": "角色语言风格 Speech Style", "description": "角色语言风格", "is_core": False},
    "tone": {"default_name": "语气", "description": "语气", "is_core": False},
    "note": {"default_name": "注意 Note", "description": "注意", "is_core": False},
}

# --- Rich Text Checker: Definitions ---
ALLOWED_IA_CONTENT_RT = {
    "B1CameraLock", "B1LightAttack", "B1DrinkBloodBottom", "B1Roll_KB",
    "Roll_GP", "B1Spell_QS", "Spell_SF", "Spell_HM", "B1HeavyAttack",
    "B1MagicArtifact", "B1UseVigorSkill", "B1Spin", "B1RideMount",
    "GSUIMap"
}
ALLOWED_GP_CONTENT_RT = {"Gamepad_FaceButton_Right"}
ALLOWED_ZHY_ID_RT = {"ia", "ib"}
ALLOWED_SIMPLE_TAGS_RT = {
    "SettingDetail_Desc", "820Privacy_Title", "EquipDetail_LegendDesc_KW",
    "EquipDetail_SuitDesc_State", "EquipDetail_SuitDesc_KW"
}
PLACEHOLDER_TAG_PATTERN_RT = re.compile(r"^\{0\}_(Title|KW)$")
GENERAL_TAG_FINDER_RT = re.compile(r"(<(zhy\s+id=\"(?:ia|ib)\"\s+txt=\".*?\"|[^>]+?)>)([\s\S]*?)</>")

# --- Translation Validator: Definitions ---
STANDARD_PROMPT_TEXT_VALIDATOR = """您是一位专业的翻译质量评估专家，请根据以下信息对翻译进行分析评估：

【分类】：{category}
【原文】：{original_text}
【译文】：{translated_text}
【角色相关信息】：{character_info}
【翻译背景信息】：{translation_background}

请按照以下17个维度进行评估，每个维度给出\"不合格\"、\"合格\"、\"良好\"或\"优秀\"的评价，并简要说明原因：

1.准确性-译文表达的语义相对于原文的忠实度：忠实度高为【优秀】，不忠实为【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
2.准确性-译文的语法和拼写正确度：语法、拼写都正确为【优秀】，不正确为【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
3.流畅性-译文的语言自然度：译文语言自然流畅为【优秀】，直译、生硬不流畅为【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
4.流畅性-译文的可读性优劣：译文可读性优为【优秀】，可读性差为【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
5.文化适应性-译文对文化隐喻（如原文有文化隐喻）的转换的得当度：在译文/原文包含文化隐喻的情况下，译文对文化隐喻转化自然得当为【优秀】，译文对文化隐喻转化生硬晦涩则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可；如译文/原文不包含文化隐喻，则跳过此维度评价
6.文化适应性-译文有对当地文化禁忌进行软着陆处理的优劣：在原文包含在译文locale的文化禁忌的情况下，译文对该文化禁忌转化自然得当为【优秀】，译文对该文化禁忌未转化则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可；如原文不包含这样的文化禁忌，则跳过此维度评价
7.文化适应性-译文在符合本地化表达方面表现优劣：在原文用了较为本地化的方式（如谚语、俚语等）表达语义时，译文能用恰当的本地化的方式表达原文的意思，并且不打破原文塑造的世界观沉浸感为【优秀】，译文不能用恰当的本地化的方式表达原文的意思且表意不清为【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可；如原文不包含这样的本地化表达，则跳过此维度评价
8.风格一致性-译文与原文用词词源年代的一致度：译文大部分用词的词源与原文用词词源年代一致，则【优秀】，译文大部分用词的词源与原文用词词源年代不一致，则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
9.风格一致性-译文与原文语言风格的一致度：译文语言风格与原文语言风格高度一致，则【优秀】，译文语言风格与原文语言风格高度不一致为【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
10.风格一致性-译文与原文语气的一致度：译文与原文语气高度一致则【优秀】，译文与原文语气高度不一致则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
11.风格一致性-译文与原文强调的重点的一致度：译文与原文强调的重点高度一致则【优秀】，高度不一致则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
12.风格一致性-译文与原文具备的美感的一致度：译文与原文的美感高度一致则【优秀】，高度不一致则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
13.游戏化表达-译文的易理解度：译文与原文的可读性高度一致则【优秀】，高度不一致则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
14.游戏化表达-译文是否足够简洁不拖沓：译文与原文的简洁度高度一致则【优秀】，高度不一致则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
15.游戏化表达-译文的表达方式与ARPG游戏的一般表达方式一致度：译文表达方式高度符合当地ARPG游戏的表达惯例，则【优秀】，高度不符合则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可
16.参考服从性-译文的翻译方向是否参考了给定的【翻译背景信息】：译文的翻译方向高度符合给定的【翻译背景信息】则【优秀】，高度不符合则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可；如该条数据未给定【翻译背景信息】则跳过该维度
17.参考服从性-译文的翻译方向是否参考了给定的【角色相关信息】：译文的翻译方向高度符合给定的【角色相关信息】则【优秀】，高度不符合则【不合格】；【合格】与【良好】为中间质量评价，视实际译文质量表现评价即可；如该条数据未给定【角色相关信息】则跳过该维度

请严格用以下格式输出评估结果：
【维度名称】：【质量】：评价等级；【原因】：简要说明

如：
【准确性-译文表达的语义相对于原文的忠实度】：【质量】：不合格；【原因】：译文\"Le sort des gouaïs est bien meilleur\"完全脱离原文\"our home\"的核心概念，将\"home\"曲解为\"the fate of gouaïs\"（gouaïs是虚构生物），且\"nowhere better than\"被完全改写为\"bien meilleur\"，语义严重偏离。
【文化适应性-译文在符合本地化表达方面表现】：【质量】：跳过；【原因】：原文不含文化隐喻，因此该维度不适用。
【风格一致性-译文与原文语言风格的一致度】：【质量】：不合格；【原因】：原文要求口语化、打油诗风格，译文却采用书面化表达，且未体现\"limericks\"的押韵节奏。
【风格一致性-译文与原文语气的一致度】：【质量】：不合格；【原因】：原文通过\"our home\"体现归属感，译文\"Le sort des gouaïs\"完全改变语义焦点，且未传达角色应有的居高临下语气。
【风格一致性-译文与原文强调的重点的一致度】：【质量】：不合格；【原因】：原文强调\"home\"的优越性，译文强调\"the fate of gouaïs\"的优越性，核心信息完全错位。
【风格一致性-译文与原文具备的美感的一致度】：【质量】：不合格；【原因】：原文具有押韵美感（home/better），译文未实现押韵，且创造生硬表述破坏美感。
【游戏化表达-译文的易理解度】：【质量】：不合格；【原因】：译文使用非游戏化术语\"sort des gouaïs\"，玩家难以理解其与原文\"our home\"的关联。
【游戏化表达-译文的表达方式与ARPG游戏的一般表达方式一致度】：【质量】：不合格；【原因】：未使用ARPG常见的简短有力台词形式，反而创造复杂句式。
【参考服从性-译文是否参考翻译背景信息】：【质量】：不合格；【原因】：完全未体现\"押韵Need to Rhyme,打油诗Ballad\"的要求，也未保持口语化风格。
【参考服从性-译文是否参考角色相关信息】：【质量】：不合格；【原因】：译文未体现\"Lesser guai A应使用不那么有教养的语言\"的要求，反而使用正式表达。

【流畅性-译文的语言自然度】：【质量】：合格；【原因】：虽然语法正确，但\"Le sort des gouaïs\"作为主语与后文\"est bien meilleur\"的搭配缺乏自然衔接，且\"des gouaïs\"的指代不明导致整体语义断裂。
【流畅性-译文的可读性优劣】：【质量】：合格；【原因】：译文符合法语基本语法规则，但因语义错位导致读者需额外解码才能理解，可读性受损。
【游戏化表达-译文是否足够简洁不拖沓】：【质量】：合格；【原因】：句长与原文相当，但因语义错位导致信息冗余。
【准确性-译文的语法和拼写正确度】：【质量】：优秀；【原因】：法语语法结构完整，拼写无错误。

每个维度一行，用换行符分隔。"""

RHYMING_PROMPT_TEXT_VALIDATOR = """您是一位专业的翻译质量评估专家，请根据以下信息对翻译进行分析评估：

【分类】：{category}
【原文】：{original_text}
【译文】：{translated_text}
【角色相关信息】：{character_info}
【翻译背景信息】：{translation_background}

请按照以下标准进行评估，每个维度给出\"合格\"或\"不合格\"的评价，并简要说明原因：

【押韵情况】：译文是否按照【翻译背景信息】中要求押韵，若译文押韵则【合格】，不押韵则【不合格】

请用以下格式输出评估结果：
【押韵情况】：【质量】：评价等级；【原因】：简要说明"""

# --- API 配置 ---
# Qwen3 Configuration (for Standard Check)
QWEN3_BASE_URL_VALIDATOR = "http://192.168.2.78:3000/v1" # 固定URL
QWEN3_MODEL_VALIDATOR = "Ali-qwen3-235b-a22b" # 固定模型名称

# DeepSeek-V3 Configuration (for Rhyme Check)
DEEPSEEK_BASE_URL_VALIDATOR = "http://192.168.2.78:3000/v1" # 固定URL
DEEPSEEK_MODEL_VALIDATOR = "ds-v3" # 固定模型名称

# General API settings
MAX_API_RETRIES_VALIDATOR = 3
API_RETRY_BASE_DELAY_SECONDS_VALIDATOR = 15
SAMPLE_SIZE_VALIDATOR = 10
VALIDATOR_MAX_WORKERS_DEFAULT = 5 # Default number of workers for AI validation

# --- Literal Translator: Definitions ---
LITERAL_TRANSLATOR_MODEL = "Ali-qwen3-32b" # 固定模型名称
LITERAL_TRANSLATOR_BASE_URL = "http://192.168.2.78:3000/v1" # 固定URL
LITERAL_TRANSLATOR_MAX_RETRIES = 3
LITERAL_TRANSLATOR_SAMPLE_SIZE = 5 # Reduced sample size for quicker feedback (Note: this seems to be for a standalone module, not the integrated one)

# --- Translation Decomposition: Definitions ---
DECOMPOSITION_MODEL = "Ali-qwen3-32b" # 固定模型名称
DECOMPOSITION_BASE_URL = "http://192.168.2.78:3000/v1" # 固定URL
DECOMPOSITION_PROMPT = """你好，你现在是一个强大的外语拆解引擎，我将会给你一个句子请你将其拆解成以下的形式，同时，在后面加上他的中文翻译，如：
"句子1：……
单词1：……
中文意思：

单词2：……
中文意思：
……

语法1：……
使用方式：
语法2：……
使用方式：
……\n\n

句子意思：……（中文）

句子2：……
单词1：……

单词2：……
……

语法1：……
语法2：……
……
句子意思：……"

当然，并不是每次都有多个句子，拆解我提供给你的句子即可。"""
DECOMPOSITION_MAX_RETRIES = 3
DECOMPOSITION_SAMPLE_SIZE = 5 # (Note: this also seems to be for a standalone module)


# --- Workflow Steps (确保CLI可用) ---
STEP_PREPROCESS = "1. 数据预处理"
STEP_RICH_TEXT_CHECK = "2. 富文本检查"
STEP_TRANSLATION_VALIDATION = "3. AI翻译校验"
STEP_TRANSLATION_SCORING = "4. AI翻译评分"

WORKFLOW_STEPS = [
    STEP_PREPROCESS,
    STEP_RICH_TEXT_CHECK,
    STEP_TRANSLATION_VALIDATION,
    STEP_TRANSLATION_SCORING
]

# --- Default configurations for CLI (可以被命令行参数或配置文件覆盖) ---

# 默认列映射 (预处理步骤)
# 在CLI模式下，这些映射可能需要更灵活的配置方式，例如通过JSON配置文件
DEFAULT_COLUMN_MAPPINGS_PREPROCESS = {
    "record_id": "Record ID",
    "file_name": "文件名 File Name",
    "source_text": "English (United Kingdom)",
    "translation_text": "Translation",
    # 非核心字段可以有默认值，也可以要求用户在配置文件中明确指定
    "speaker": "说话人 Speaker",
    "comments": "EN Source Comments",
    "path": "路径 Path",
    "scenario": "Scenario",
    "char_intro": "角色介绍 Character Intro",
    "char_style": "角色语言风格 Speech Style",
    "tone": "语气",
    "note": "注意 Note"
}

# 富文本检查默认键名
DEFAULT_PREPROCESS_ORIGINAL_TEXT_KEY = "原文"
DEFAULT_PREPROCESS_TRANSLATED_TEXT_KEY = "译文"
# AI校验样本大小 (如果CLI版本仍需要样本阶段)
DEFAULT_VALIDATOR_SAMPLE_SIZE = SAMPLE_SIZE_VALIDATOR # 使用上面定义的常量

# 默认输出文件名 (CLI可以使用这些作为基础，加上前缀或时间戳)
DEFAULT_PREPROCESS_OUTPUT_JSON_NAME = "processed_data.json"
DEFAULT_RICH_TEXT_OUTPUT_EXCEL_NAME = "rich_text_check_report.xlsx"
DEFAULT_VALIDATION_OUTPUT_EXCEL_NAME = "validation_results.xlsx"
DEFAULT_SCORING_OUTPUT_EXCEL_NAME = "translation_scored_report.xlsx" 

OPENAI_CONNECT_TIMEOUT_SECONDS = 10  # Example value
OPENAI_REQUEST_TIMEOUT_SECONDS = 120