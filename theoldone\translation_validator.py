import streamlit as st
import pandas as pd
import json
import re
import time
from openai import OpenAI, APIStatusError,APITimeoutError
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from config import (
    MAX_API_RETRIES_VALIDATOR, API_RETRY_BASE_DELAY_SECONDS_VALIDATOR,
    LITERAL_TRANSLATOR_MODEL, DECOMPOSITION_MODEL,
    LITERAL_TRANSLATOR_BASE_URL, DECOMPOSITION_BASE_URL,
    # Constants for OpenAI client timeouts
    OPENAI_CONNECT_TIMEOUT_SECONDS, 
    OPENAI_REQUEST_TIMEOUT_SECONDS
)
from literal_translator import translate_text_literal
from translation_decomposer import explain_translation_text_decomposition_adapted


def parse_and_categorize_analysis_validator(analysis_string):
    if not analysis_string or not isinstance(analysis_string, str):
        return "", False, 0

    if analysis_string.startswith("分析错误:") or analysis_string.startswith("任务执行错误:"):
        return analysis_string, True, -1 

    line_pattern = re.compile(
        r"^\s*(?:\d+\.?\s*)?(.*?)\s*[:：]\s*(?:【质量】|【评级】)\s*[:：]\s*(.*?)\s*[;；]\s*(?:【原因】\s*[:：]\s*)?(.*)$"
    )
    unqualified_list = []
    qualified_list = []
    good_list = []
    excellent_list = []
    has_unqualified = False
    highest_level = -1 

    for line in analysis_string.splitlines():
        stripped_line = line.strip()
        if not stripped_line:
            continue
        if stripped_line.startswith(("（注：", "注：", "---", "*注：", "※未涉及维度说明：", "- 文化隐喻", "- 参考服从性")):
            continue

        match = line_pattern.match(stripped_line)
        if match:
            dimension_text_from_regex, quality_raw, reason = match.groups()
            dimension_text_stripped = dimension_text_from_regex.strip()
            dimension_no_num_prefix = re.sub(r"^\d+\.?\s*", "", dimension_text_stripped)
            qwen_style_prefix = "【维度名称】："
            if dimension_no_num_prefix.startswith(qwen_style_prefix):
                dimension = dimension_no_num_prefix[len(qwen_style_prefix):].strip()
            else:
                dimension = dimension_no_num_prefix
            
            reason = reason.strip()
            quality_stripped = quality_raw.strip()
            quality = re.sub(r"[:：].*$", "", quality_stripped).strip().replace("【", "").replace("】", "")

            detail_line = f"{dimension}：【质量】：{quality}；【原因】：{reason}"

            valid_ratings = ["合格", "良好", "优秀", "不合格"]
            skip_ratings = [
                "不适用", "N/A", "跳过", "-", "/", "未评价",
                "该维度跳过", "无", "未提供信息", "未提供跳过",
                "跳过此维度评价"
            ]

            if quality in skip_ratings:
                continue 
            elif quality not in valid_ratings:
                continue 

            if quality == "不合格":
                unqualified_list.append(detail_line)
                has_unqualified = True
                highest_level = max(highest_level, 0)
            elif quality == "合格":
                qualified_list.append(detail_line)
                highest_level = max(highest_level, 1)
            elif quality == "良好":
                good_list.append(detail_line)
                highest_level = max(highest_level, 2)
            elif quality == "优秀":
                excellent_list.append(detail_line)
                highest_level = max(highest_level, 3)

    if not unqualified_list and not qualified_list and not good_list and not excellent_list:
        if analysis_string and highest_level == -1:
             pass
        return "", False, 0

    all_details_parts = []
    if unqualified_list:
        all_details_parts.append("\n".join(unqualified_list))
    other_parts_list = qualified_list + good_list + excellent_list
    if other_parts_list:
        all_details_parts.append("\n".join(other_parts_list))
    separator = "\n\n" if unqualified_list and other_parts_list else "\n"
    final_analysis_string = separator.join(filter(None, all_details_parts))

    return final_analysis_string, has_unqualified, highest_level

def _prepare_excel_row_data_validator(key, analysis_data):
    orig_text = analysis_data.get("original_text", "")
    trans_text = analysis_data.get("translated_text", "")
    
    std_analysis_tuple = analysis_data.get("standard_analysis")
    rhy_analysis_tuple = analysis_data.get("rhyme_analysis") 
    
    analysis_parts_for_cell = []
    overall_has_unqualified = False
    sort_level_from_standard = -1
    std_text = ""
    rhyme_text = ""
    rhyme_is_unqualified = False

    if std_analysis_tuple and len(std_analysis_tuple) >= 3:
        std_text, has_unq, level = std_analysis_tuple
        if has_unq: overall_has_unqualified = True
        sort_level_from_standard = level
    else:
        std_text = std_analysis_tuple[0] if std_analysis_tuple and len(std_analysis_tuple) > 0 else "错误：标准分析缺失或格式错误"
        overall_has_unqualified = True 
        sort_level_from_standard = -1

    if rhy_analysis_tuple and len(rhy_analysis_tuple) >= 3:
        rhyme_text_raw, rhyme_has_unq, _ = rhy_analysis_tuple
        rhyme_text = "---押韵专项分析---\n" + rhyme_text_raw
        if rhyme_has_unq:
            rhyme_is_unqualified = True
            overall_has_unqualified = True 
    elif rhy_analysis_tuple: 
        rhyme_text = "---押韵专项分析---\n" + str(rhy_analysis_tuple[0])
        rhyme_is_unqualified = True 
        overall_has_unqualified = True
        
    if rhyme_is_unqualified and rhyme_text:
        analysis_parts_for_cell.append(rhyme_text)
    if std_text:
        analysis_parts_for_cell.append(std_text)
    if not rhyme_is_unqualified and rhyme_text:
         analysis_parts_for_cell.append(rhyme_text)
    
    separator = "\n\n" if std_text and rhyme_text else "\n"
    final_combined_text = separator.join(filter(None, analysis_parts_for_cell))

    if overall_has_unqualified:
         final_excel_sort_level = -1 if sort_level_from_standard == -1 else 0
    else:
         final_excel_sort_level = sort_level_from_standard

    analysis_note = final_combined_text
    if overall_has_unqualified and not final_combined_text.startswith("错误：") and not final_combined_text.startswith("处理被用户停止"):
         analysis_note = "⚠️ " + final_combined_text
    
    literal_trans = analysis_data.get("literal_translation", "N/A")
    decomp_analysis = analysis_data.get("decomposition_analysis", "N/A")
        
    return {
        "Key": key,
        "Original": orig_text[:100] + "..." if len(orig_text) > 100 else orig_text,
        "Translated": trans_text[:100] + "..." if len(trans_text) > 100 else trans_text,
        "Analysis": analysis_note,
        "LiteralTranslation": literal_trans, 
        "Decomposition": decomp_analysis,   
        "HasUnqualified": overall_has_unqualified,
        "SortLevel": final_excel_sort_level
    }

def analyze_translation_validator(api_clients, key, item_data, prompt_to_use, task_type, model_to_use, log_callback=None):
    should_stop = False
    try:
        should_stop = st.session_state.validator_should_stop
    except (KeyError, AttributeError):
        should_stop = False
    
    def _log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)
    
    # Initialize texts early for consistent return
    original_text = item_data.get("原文", "")
    translated_text = item_data.get("译文", "")

    if should_stop:
        _log(f"[ACTIVITY_LOG_STOP] Task for key {key} stopped by user flag.")
        return key, original_text, translated_text, "处理被用户停止", "stopped", "Stopped by user", None 

    retries = 0
    
    if not original_text or not translated_text:
        return key, original_text, translated_text, "原文或译文为空", "error", "Input Error", None # 7th is toast_msg
        
    extra_params = {}
    client_options = {
        "timeout": OPENAI_REQUEST_TIMEOUT_SECONDS,
        # Note: max_retries is often handled by the client upon instantiation or per-request for some API error types.
        # Here, we are implementing a manual retry for 429, but client-level retries for other transient issues might also occur.
    }

    if task_type == 'standard':
        selected_client = api_clients['qwen']
        selected_model = model_to_use 
        extra_params = {"extra_body": {"enable_thinking": False}}
    elif task_type == 'rhyme':
        selected_client = api_clients['deepseek']
        selected_model = model_to_use
    else:
        return key, original_text, translated_text, f"未知的任务类型: {task_type}", "error", "Config Error", None # 7th is toast_msg

    while retries <= MAX_API_RETRIES_VALIDATOR:
        try:
            if selected_client is None: 
                return key, original_text, translated_text, "API客户端未正确初始化", "error", "API Client Error", None # 7th is toast_msg

            analysis_result = ""
            request_start_time = time.time()
            current_activity_message = f"条目 {key} ({task_type}): 调用 {selected_model}..."
            try:
                if 'validator_current_activity' in st.session_state:
                    st.session_state.validator_current_activity = current_activity_message
                    print(f"[DEBUG_ACTIVITY_UPDATE] Set activity for {key} to: {current_activity_message}") # Console print
            except Exception as e_ss_update:
                print(f"[DEBUG_ACTIVITY_UPDATE_ERROR] Failed to set activity for {key}: {e_ss_update}") # Console print
            _log(f"[ACTIVITY_LOG] {current_activity_message}")


            if task_type == 'standard':
                response_stream = selected_client.chat.completions.create(
                    model=selected_model, 
                    messages=[{"role": "system", "content": "您是一位专业的翻译质量评估专家，精通多语言和文化背景。"}, {"role": "user", "content": prompt_to_use}],
                    temperature=0.5, stream=True, **extra_params,
                    timeout=OPENAI_REQUEST_TIMEOUT_SECONDS # Pass timeout here
                )
                analysis_result = "".join(chunk.choices[0].delta.content for chunk in response_stream if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content)
            else: 
                response = selected_client.chat.completions.create(
                    model=selected_model, 
                    messages=[{"role": "system", "content": "您是一位专业的翻译质量评估专家，精通多语言和文化背景。"}, {"role": "user", "content": prompt_to_use}],
                    temperature=0.5, **extra_params,
                    timeout=OPENAI_REQUEST_TIMEOUT_SECONDS # Pass timeout here
                )
                analysis_result = response.choices[0].message.content
            
            request_duration = time.time() - request_start_time
            _log(f"[ACTIVITY_LOG] 条目 {key} ({task_type}): {selected_model} 调用完成, 耗时 {request_duration:.2f}s.")
            activity_done_message = f"条目 {key} ({task_type}): 处理完成."
            try:
                if 'validator_current_activity' in st.session_state:
                     st.session_state.validator_current_activity = activity_done_message
                     print(f"[DEBUG_ACTIVITY_UPDATE] Set activity for {key} to: {activity_done_message}") # Console print
            except Exception as e_ss_update_done:
                print(f"[DEBUG_ACTIVITY_UPDATE_ERROR] Failed to set activity (done) for {key}: {e_ss_update_done}") # Console print

            if not analysis_result or len(analysis_result) < 10: 
                _log(f"[API_RESPONSE_ERROR] Key {key}, Task {task_type}: API返回内容过短或为空. Length: {len(analysis_result)}")
                return key, original_text, translated_text, "API返回内容过短或为空", "error", f"Short Response (Length: {len(analysis_result)})", None 
            
            return key, original_text, translated_text, analysis_result, "success", f"Success (Length: {len(analysis_result)})", None 

        except APITimeoutError as e: # Specific timeout error from OpenAI SDK
            retries += 1
            error_message_for_toast = f"条目 {key} ({task_type}) API调用超时 (尝试 {retries}/{MAX_API_RETRIES_VALIDATOR+1})。"
            _log(f"[API_ERROR_LOG] {error_message_for_toast} Details: {e}")
            if retries <= MAX_API_RETRIES_VALIDATOR:
                delay = API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2 ** (retries -1))
                time.sleep(delay)
                should_stop_in_retry = False
                try: should_stop_in_retry = st.session_state.validator_should_stop
                except (KeyError, AttributeError): pass
                if should_stop_in_retry: 
                    return key, original_text, translated_text, f"处理在重试等待期间被用户停止 (超时错误: {e})", "stopped", f"Stopped during retry for {key}", error_message_for_toast
                continue
            else: # Max retries for timeout reached
                final_error_msg = f"分析错误: API调用连续超时 {retries} 次. {e}"
                return key, original_text, translated_text, final_error_msg, "error", f"API Timeout Error after retries (Type: {type(e).__name__})", error_message_for_toast

        except APIStatusError as e:
            status_code = e.status_code
            error_message_for_toast = f"条目 {key} ({task_type}) API错误 (HTTP {status_code}): {str(e.message)[:100]}..."
            _log(f"[API_ERROR_LOG] {error_message_for_toast} Details: {e}")

            specific_error_info = f"HTTP状态码: {status_code}. "
            if status_code == 400:
                specific_error_info += "请求无效 (Bad Request)。请检查输入内容或参数是否符合API要求。"
            elif status_code == 401:
                specific_error_info += "认证失败 (Unauthorized)。请检查API密钥是否正确且有效。"
            elif status_code == 403:
                specific_error_info += "禁止访问 (Forbidden)。API密钥可能没有权限访问该模型或服务。"
            elif status_code == 404:
                specific_error_info += "未找到 (Not Found)。请求的API端点或模型可能不存在。"
            elif status_code == 429: # Rate limit or quota
                specific_error_info += "请求过于频繁或超出配额 (Too Many Requests)。"
                if retries < MAX_API_RETRIES_VALIDATOR:
                    retries += 1
                    # Use 'Retry-After' header if available, otherwise exponential backoff
                    retry_after_seconds = e.response.headers.get("Retry-After")
                    delay = API_RETRY_BASE_DELAY_SECONDS_VALIDATOR * (2 ** (retries -1))
                    if retry_after_seconds:
                        try: delay = max(delay, float(retry_after_seconds)) # Respect server's request
                        except ValueError: pass # If header is not a number
                    
                    error_message_for_toast = f"条目 {key} ({task_type}) API请求超限 (HTTP 429)，将在 {delay:.1f}s 后重试 ({retries}/{MAX_API_RETRIES_VALIDATOR+1})."
                    _log(f"[API_RETRY_LOG] {error_message_for_toast}")
                    time.sleep(delay)
                    should_stop_in_retry = False
                    try: should_stop_in_retry = st.session_state.validator_should_stop
                    except (KeyError, AttributeError): pass
                    if should_stop_in_retry: 
                        return key, original_text, translated_text, f"处理在重试等待期间被用户停止 (错误: {e.message})", "stopped", f"Stopped during retry for {key}", error_message_for_toast
                    continue
                else: # Max retries for 429 reached
                    final_error_msg = f"分析错误: API请求连续超限 (HTTP 429) {retries} 次. {e.message}"
                    return key, original_text, translated_text, final_error_msg, "error", f"API Rate Limit Error after retries (Type: {type(e).__name__})", error_message_for_toast
            elif status_code >= 500: # Server-side errors
                specific_error_info += "服务器内部错误 (Server Error)。请稍后重试或联系服务提供商。"
                # Could implement retries for 5xx errors as well if desired, similar to 429
            else: # Other client-side errors
                specific_error_info += f"客户端错误。错误详情: {e.message}"

            final_error_msg = f"分析错误 (Retries: {retries}, Type: {type(e).__name__}): {specific_error_info} {e.message}"
            return key, original_text, translated_text, final_error_msg, "error", f"API Error (Code: {status_code}, Type: {type(e).__name__})", error_message_for_toast
            
        except Exception as e: # Catch-all for other exceptions
            error_type_for_return = f"任务执行错误 (Type: {type(e).__name__})"
            error_message_for_toast = f"条目 {key} ({task_type}) 发生意外错误: {str(e)[:100]}..."
            _log(f"[UNEXPECTED_ERROR_LOG] {error_message_for_toast} Details: {e}")
            import traceback
            _log(f"[UNEXPECTED_ERROR_TRACE] {traceback.format_exc()}")

            # For unexpected errors, typically do not retry unless known to be transient.
            # If retries are desired here, add similar logic to 429.
            final_error_msg = f"任务执行错误 (Retries: {retries}, Type: {type(e).__name__}): {str(e)}"
            return key, original_text, translated_text, final_error_msg, "error", error_type_for_return, error_message_for_toast

    # If loop finishes, it means all retries failed (e.g. for 429 or timeout that kept retrying)
    # This path should ideally not be reached if the logic inside the loop correctly returns or continues.
    # However, as a fallback:
    all_retries_failed_msg = f"分析错误: 所有 {MAX_API_RETRIES_VALIDATOR + 1} 次尝试均失败 (条目: {key}, 类型: {task_type})."
    _log(f"[MAX_RETRIES_LOG] {all_retries_failed_msg}")
    return key, original_text, translated_text, all_retries_failed_msg, "error", "Max Retries Exhausted", f"条目 {key} ({task_type}) 所有重试均失败。"


def run_validation_batch(api_clients, tasks_to_run, qwen_model, deepseek_model):
    processed_item_keys_in_batch = set() 
    total_tasks_in_batch = len(tasks_to_run)
    completed_in_batch = 0
    
    batch_id = f"batch_{time.strftime('%Y%m%d_%H%M%S')}_{hash(frozenset([t.get('key', '') for t in tasks_to_run])) % 10000}"
    st.session_state[f"validator_batch_{batch_id}_started"] = True

    tasks_copy = list(tasks_to_run)
    
    checkpoint_start = 0
    if 'validator_checkpoint_counter' in st.session_state:
        checkpoint_start = st.session_state.validator_checkpoint_counter

    global_completed_before = st.session_state.validator_progress.get("completed", 0)
    global_total = st.session_state.validator_progress.get("total", 0)

    st.session_state.validator_items_check_completed = set()
    st.session_state.validator_items_literal_completed = set()
    st.session_state.validator_items_decompose_completed = set()

    if global_completed_before > global_total and global_total > 0:
        global_completed_before = global_total

    progress_ui = {}
    st.markdown("---校验进度---")
    progress_ui['check'] = {"bar": st.progress(0), "text": st.empty()}
    if st.session_state.get('validator_enable_literal_translation', False) and st.session_state.validator_progress_literal["total"] > 0:
        st.markdown("---中英直译进度---")
        progress_ui['literal'] = {"bar": st.progress(0), "text": st.empty()}
    if st.session_state.get('validator_enable_decomposition', False) and st.session_state.validator_progress_decompose["total"] > 0:
        st.markdown("---翻译拆解进度---")
        progress_ui['decompose'] = {"bar": st.progress(0), "text": st.empty()}

    temp_results = {}
    
    literal_translator_api_client = None
    decomposition_api_client = None
    try:
        if st.session_state.literal_translator_base_url and st.session_state.api_keys.get("qwen"):
            literal_translator_api_client = OpenAI(
                api_key=st.session_state.api_keys["qwen"], 
                base_url=st.session_state.literal_translator_base_url
            )
        if st.session_state.decomposition_base_url and st.session_state.api_keys.get("qwen"):
            decomposition_api_client = OpenAI(
                api_key=st.session_state.api_keys["qwen"], 
                base_url=st.session_state.decomposition_base_url
            )
    except Exception as client_init_e:
        st.warning(f"初始化直译/拆解API客户端失败: {client_init_e}，相关步骤将被跳过。")

    try:
        with ThreadPoolExecutor(max_workers=min(10, len(tasks_copy))) as executor:
            futures_map = {}
            
            for task_idx, task in enumerate(tasks_copy):
                if task_idx < checkpoint_start:
                    continue
                    
                model_for_this_task = None
                if task['type'] == 'standard':
                    model_for_this_task = qwen_model
                elif task['type'] == 'rhyme':
                    model_for_this_task = deepseek_model
                else:
                    st.toast(f"未知任务类型 {task['type']}，跳过任务: {task['key']}", icon="❓")
                    continue

                if (task['key'] in st.session_state.validator_checkpoint_results and 
                    task['type'] in st.session_state.validator_checkpoint_results[task['key']]):
                    processed_item_keys_in_batch.add(task['key'])
                    completed_in_batch += 1
                    continue

                future = executor.submit(
                    analyze_translation_validator,
                    api_clients,
                    task['key'],
                    task['item'],
                    task['prompt'],
                    task['type'],
                    model_for_this_task
                )
                futures_map[future] = (task_idx, task['key'], task['type'], task['item'])
            
            for future in as_completed(futures_map):
                should_stop = False
                try: 
                    should_stop = st.session_state.validator_should_stop
                except (KeyError, AttributeError): 
                    pass
                
                if should_stop: 
                    st.session_state.validator_checkpoint_counter = completed_in_batch
                    for key, data in temp_results.items():
                        if key not in st.session_state.validator_checkpoint_results:
                            st.session_state.validator_checkpoint_results[key] = {}
                        st.session_state.validator_checkpoint_results[key].update(data)
                    break

                task_idx, original_key, prompt_type, original_item_data = futures_map[future]
                
                try:
                    future_result = future.result()
                    key, orig_text, trans_text, analysis_raw, status_type, status_detail, toast_msg = future_result[:7]

                    if toast_msg:
                        st.toast(toast_msg, icon="🔥") 
                    elif status_type == "error":
                        st.toast(f"处理 {key} ({prompt_type}) 失败: {analysis_raw[:50]}... 详情: {status_detail}", icon="🔥")
                    elif status_type == "stopped":
                        st.toast(f"处理 {key} ({prompt_type}) 已停止. {status_detail}", icon="🛑")
                    
                    if key not in temp_results:
                        temp_results[key] = {}
                    
                    if "original_text" not in temp_results[key]:
                        temp_results[key]["original_text"] = orig_text or original_item_data.get("原文", "")
                    if "translated_text" not in temp_results[key]:
                        temp_results[key]["translated_text"] = trans_text or original_item_data.get("译文", "")
                    
                    if status_type == "stopped" or status_type == "error":
                        temp_results[key][f"{prompt_type}_analysis"] = (analysis_raw, True, -1)
                        temp_results[key]["literal_translation"] = "校验失败，跳过直译"
                        temp_results[key]["decomposition_analysis"] = "校验失败，跳过拆解"
                    else:
                        temp_results[key][f"{prompt_type}_analysis"] = parse_and_categorize_analysis_validator(analysis_raw)
                        
                        current_trans_text = temp_results[key]["translated_text"] 

                        if literal_translator_api_client and current_trans_text and st.session_state.get('validator_enable_literal_translation', False):
                            try:
                                literal_translation_result = translate_text_literal(
                                    literal_translator_api_client,
                                    st.session_state.literal_translator_model_name, # This should be LITERAL_TRANSLATOR_MODEL
                                    current_trans_text
                                )
                                temp_results[key]["literal_translation"] = literal_translation_result
                                if isinstance(literal_translation_result, str) and literal_translation_result.startswith("中英直译错误:"):
                                    st.toast(f"条目 {key}: {literal_translation_result}", icon="⚠️")
                            except Exception as lt_e:
                                error_msg_lt = f"中英直译错误: {str(lt_e)}"
                                temp_results[key]["literal_translation"] = error_msg_lt
                                st.toast(f"条目 {key}: {error_msg_lt}", icon="⚠️")
                        elif not st.session_state.get('validator_enable_literal_translation', False):
                            temp_results[key]["literal_translation"] = "中英直译未启用"
                        elif not literal_translator_api_client:
                            temp_results[key]["literal_translation"] = "中英直译跳过 (API客户端未初始化)"
                        else:
                            temp_results[key]["literal_translation"] = "中英直译跳过 (无译文)"

                        if decomposition_api_client and current_trans_text and st.session_state.get('validator_enable_decomposition', False):
                            try:
                                decomposition_analysis_result = explain_translation_text_decomposition_adapted(
                                    decomposition_api_client,
                                    st.session_state.decomposition_model_name, # This should be DECOMPOSITION_MODEL
                                    current_trans_text
                                )
                                temp_results[key]["decomposition_analysis"] = decomposition_analysis_result
                                if isinstance(decomposition_analysis_result, str) and decomposition_analysis_result.startswith("翻译拆解错误:"):
                                    st.toast(f"条目 {key}: {decomposition_analysis_result}", icon="⚠️")
                            except Exception as dc_e:
                                error_msg_dc = f"翻译拆解错误: {str(dc_e)}"
                                temp_results[key]["decomposition_analysis"] = error_msg_dc
                                st.toast(f"条目 {key}: {error_msg_dc}", icon="⚠️")
                        elif not st.session_state.get('validator_enable_decomposition', False):
                            temp_results[key]["decomposition_analysis"] = "翻译拆解未启用"
                        elif not decomposition_api_client:
                            temp_results[key]["decomposition_analysis"] = "翻译拆解跳过 (API客户端未初始化)"
                        else:
                            temp_results[key]["decomposition_analysis"] = "翻译拆解跳过 (无译文)"
                    
                    if key not in st.session_state.validator_aggregated_results:
                        st.session_state.validator_aggregated_results[key] = defaultdict(lambda: None) 
                    
                    st.session_state.validator_aggregated_results[key]["original_text"] = temp_results[key].get("original_text")
                    st.session_state.validator_aggregated_results[key]["translated_text"] = temp_results[key].get("translated_text")
                    if f"{prompt_type}_analysis" in temp_results[key]:
                        st.session_state.validator_aggregated_results[key][f"{prompt_type}_analysis"] = temp_results[key][f"{prompt_type}_analysis"]
                    st.session_state.validator_aggregated_results[key]["literal_translation"] = temp_results[key].get("literal_translation")
                    st.session_state.validator_aggregated_results[key]["decomposition_analysis"] = temp_results[key].get("decomposition_analysis")
                    
                    processed_item_keys_in_batch.add(key)
                    
                    if prompt_type == 'standard': 
                        with st.session_state.validator_progress_lock:
                            if key not in st.session_state.get('validator_items_check_completed', set()):
                                st.session_state.validator_progress_check["completed"] += 1
                                st.session_state.setdefault('validator_items_check_completed', set()).add(key)
                    
                    all_modules_processed_for_key = True 
                    if st.session_state.get('validator_enable_literal_translation', False) and temp_results[key].get("literal_translation") is not None:
                        with st.session_state.validator_progress_lock:
                            if key not in st.session_state.get('validator_items_literal_completed', set()):
                                st.session_state.validator_progress_literal["completed"] += 1
                                st.session_state.setdefault('validator_items_literal_completed', set()).add(key)
                        
                        if st.session_state.get('validator_enable_decomposition', False) and temp_results[key].get("decomposition_analysis") is not None:
                            with st.session_state.validator_progress_lock:
                                if key not in st.session_state.get('validator_items_decompose_completed', set()):
                                    st.session_state.validator_progress_decompose["completed"] += 1
                                    st.session_state.setdefault('validator_items_decompose_completed', set()).add(key)

                    current_time = time.time()
                    if (current_time - st.session_state.validator_last_progress_update > 0.5 and 
                        st.session_state.validator_autosave_enabled):
                        for save_key, save_data in temp_results.items():
                            if save_key not in st.session_state.validator_checkpoint_results:
                                st.session_state.validator_checkpoint_results[save_key] = {}
                            st.session_state.validator_checkpoint_results[save_key].update(save_data)
                        
                        st.session_state.validator_checkpoint_counter = completed_in_batch
                        # last_autosave_time = current_time # This should be a session state variable if used across calls
                        st.toast(f"已自动保存翻译校验进度 ({completed_in_batch}/{total_tasks_in_batch})", icon="💾")

                except Exception as exc:
                    st.error(f'Validator: 处理任务 (Key: {original_key}, Type: {prompt_type}) 时发生主循环错误: {exc}')
                    import traceback
                    st.error(f"错误详情: {traceback.format_exc()}")
                    
                    if original_key not in temp_results:
                        temp_results[original_key] = {}
                    
                    if "original_text" not in temp_results[original_key]:
                        temp_results[original_key]["original_text"] = original_item_data.get("原文", "")
                    if "translated_text" not in temp_results[original_key]:
                        temp_results[original_key]["translated_text"] = original_item_data.get("译文", "")
                    
                    error_msg = f"处理错误: {exc}"
                    temp_results[original_key][f"{prompt_type}_analysis"] = (error_msg, True, -1)
                    temp_results[original_key]["literal_translation"] = f"主任务处理错误，跳过直译"
                    temp_results[original_key]["decomposition_analysis"] = f"主任务处理错误，跳过拆解"
                    
                    if original_key not in st.session_state.validator_aggregated_results:
                        st.session_state.validator_aggregated_results[original_key] = defaultdict(lambda: None)
                    
                    st.session_state.validator_aggregated_results[original_key]["original_text"] = temp_results[original_key].get("original_text")
                    st.session_state.validator_aggregated_results[original_key]["translated_text"] = temp_results[original_key].get("translated_text")
                    st.session_state.validator_aggregated_results[original_key][f"{prompt_type}_analysis"] = temp_results[original_key][f"{prompt_type}_analysis"]
                    st.session_state.validator_aggregated_results[original_key]["literal_translation"] = temp_results[original_key].get("literal_translation")
                    st.session_state.validator_aggregated_results[original_key]["decomposition_analysis"] = temp_results[original_key].get("decomposition_analysis")
                    
                    processed_item_keys_in_batch.add(original_key)

                finally:
                    completed_in_batch += 1
                    
                    with st.session_state.validator_progress_lock:
                        current_global_completed = global_completed_before + completed_in_batch
                        st.session_state.validator_progress["completed"] = current_global_completed
                        
                        if current_global_completed > global_total and global_total > 0:
                            current_global_completed = global_total
                            st.session_state.validator_progress["completed"] = current_global_completed
                    
                    progress_val = current_global_completed / global_total if global_total > 0 else 0.0
                    
                    current_time = time.time()
                    if current_time - st.session_state.validator_last_progress_update > 0.5: 
                        if 'check' in progress_ui:
                            comp_c = st.session_state.validator_progress_check["completed"]
                            tot_c = st.session_state.validator_progress_check["total"]
                            prog_c = comp_c / tot_c if tot_c > 0 else 0
                            progress_ui['check']["text"].text(f"翻译校验: {comp_c}/{tot_c} ({prog_c:.0%})")
                            progress_ui['check']["bar"].progress(prog_c)
                        if 'literal' in progress_ui and st.session_state.get('validator_enable_literal_translation', False):
                            comp_l = st.session_state.validator_progress_literal["completed"]
                            tot_l = st.session_state.validator_progress_literal["total"]
                            prog_l = comp_l / tot_l if tot_l > 0 else 0
                            progress_ui['literal']["text"].text(f"中英直译: {comp_l}/{tot_l} ({prog_l:.0%})")
                            progress_ui['literal']["bar"].progress(prog_l)
                        if 'decompose' in progress_ui and st.session_state.get('validator_enable_decomposition', False):
                            comp_d = st.session_state.validator_progress_decompose["completed"]
                            tot_d = st.session_state.validator_progress_decompose["total"]
                            prog_d = comp_d / tot_d if tot_d > 0 else 0
                            progress_ui['decompose']["text"].text(f"翻译拆解: {comp_d}/{tot_d} ({prog_d:.0%})")
                            progress_ui['decompose']["bar"].progress(prog_d)
                            
                        st.session_state.validator_last_progress_update = current_time

    except Exception as e:
        st.error(f"运行翻译校验批处理时发生错误: {e}")
        import traceback
        st.error(f"错误详情: {traceback.format_exc()}")
        
        for key, data in temp_results.items():
            if key not in st.session_state.validator_checkpoint_results:
                st.session_state.validator_checkpoint_results[key] = {}
            st.session_state.validator_checkpoint_results[key].update(data)
        
        st.session_state.validator_checkpoint_counter = completed_in_batch
        return set()
    finally:
        for key, data in temp_results.items():
            if key not in st.session_state.validator_checkpoint_results:
                st.session_state.validator_checkpoint_results[key] = {}
            st.session_state.validator_checkpoint_results[key].update(data)
        
        st.session_state.validator_checkpoint_counter = completed_in_batch
        
        try:
            for key_ui in progress_ui:
                progress_ui[key_ui]["bar"].empty()
                progress_ui[key_ui]["text"].empty()
        except:
            pass
        
        st.session_state[f"validator_batch_{batch_id}_completed"] = True
    return processed_item_keys_in_batch
