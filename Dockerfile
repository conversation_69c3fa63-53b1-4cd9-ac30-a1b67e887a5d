# 1. 选择一个官方的Python运行时作为父镜像
FROM python:3.9-slim

# 2. 设置工作目录
WORKDIR /usr/src/app

# 3. 防止Python将stdout/stderr输出缓冲起来
ENV PYTHONUNBUFFERED=1

# 3.5. 明确设置PYTHONPATH，确保工作目录在模块搜索路径中
ENV PYTHONPATH="/usr/src/app:${PYTHONPATH}"

# 4. 复制依赖文件
COPY requirements.txt ./

# 5. 安装Python依赖
RUN pip install --default-timeout=300 --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r requirements.txt

# 6. 复制应用代码到工作目录
# 我们会将整个 'app' 目录复制过去，它将成为 /usr/src/app/app
COPY ./app /usr/src/app/app

# 7. (可选) 如果检查点目录需要在容器构建时就存在 (通常通过volume在运行时挂载更好)
# RUN mkdir -p /usr/src/app/app/.jiaoyan_checkpoints

# 8. 定义容器启动时执行的命令
ENTRYPOINT ["python", "-m", "app.main_cli"] 