import shutil
import uuid
import os
import subprocess
import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Request, Body, BackgroundTasks, Query
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse, PlainTextResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional, Literal, List
import asyncio
import json
import datetime
import pandas as pd

# --- App specific imports ---
# Ensure the app directory is in Python's path or installed as a package.
# This might require adjustment based on your Dockerfile WORKDIR and how 'app' is copied.
# Assuming 'app' is copied to /usr/src/web_app/app and WORKDIR is /usr/src/web_app
# One way to ensure imports work if not running as a package:
import sys
# Assuming 'app' directory is parallel to 'web_server' in the container under '/usr/src/web_app'
# or that Python path is otherwise configured for the 'app' module.
# If Dockerfile copies 'app' to /usr/src/web_app/app, and main_web.py is in /usr/src/web_app/web_server
# then adding parent of 'app' to sys.path could work if 'app' is a package.
# For simplicity, assuming Dockerfile places 'app' such that it can be imported directly
# or Python path is already set up.
try:
    from app import preprocess as app_preprocess
    from app import rich_text_checker as app_rich_text_checker
    from app import translation_validator as app_translation_validator
    from app import translation_scorer as app_translation_scorer
    from app import config as g_app_config
    from app import main_cli as app_main_cli # For prepare_validation_tasks
except ImportError as e:
    logging.error(f"Failed to import 'app' modules: {e}. Ensure 'app' directory is in PYTHONPATH.")
    # Depending on severity, you might want to exit or run in a degraded mode.
    # For now, we'll let it proceed and fail later if these modules are used.
    pass

# --- Constants & Enhanced Logging Configuration ---
LOG_DIR_PATH = Path("logs") 
LOG_DIR_PATH.mkdir(parents=True, exist_ok=True)
LOG_FILE_WEB_APP = LOG_DIR_PATH / "web_server_application.log"

# Configure a specific, uniquely named logger for the application
app_specific_logger = logging.getLogger("WebServerAppLogger") # Changed name for more uniqueness
app_specific_logger.setLevel(logging.INFO) 
app_specific_logger.propagate = False # Crucial to prevent Uvicorn's root logger from duplicating these logs

# Add handler only if no handlers are already attached (to prevent duplication on Uvicorn reload)
if not app_specific_logger.hasHandlers():
    file_handler = RotatingFileHandler(LOG_FILE_WEB_APP, maxBytes=10*1024*1024, backupCount=3, encoding='utf-8')
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - [%(module)s.%(funcName)s:%(lineno)d] - %(message)s')
    file_handler.setFormatter(formatter)
    app_specific_logger.addHandler(file_handler)
    
    # Optional: Add a StreamHandler to also see these logs in the console during development
    # This is helpful if Uvicorn's own logging is set to a higher level like WARNING
    # console_handler = logging.StreamHandler(sys.stdout) # import sys needed
    # console_handler.setFormatter(formatter)
    # console_handler.setLevel(logging.DEBUG) # Or INFO
    # app_specific_logger.addHandler(console_handler)

app_specific_logger.info("WebServerAppLogger initialized and configured to log to file and potentially console.")

UPLOADS_DIR = Path("uploads") 
OUTPUTS_DIR = Path("outputs") 
TEMPLATES_DIR = Path("templates")

UPLOADS_DIR.mkdir(parents=True, exist_ok=True)
OUTPUTS_DIR.mkdir(parents=True, exist_ok=True)

# FastAPI app setup
app = FastAPI()

# --- Middleware to log validation error details ---
@app.middleware("http")
async def log_validation_error_detail_middleware(request: Request, call_next):
    response = await call_next(request)
    if response.status_code == 422 and "/initiate_processing/" in str(request.url) and request.method == "POST":
        response_body_bytes = b""
        async for chunk in response.body_iterator:
            response_body_bytes += chunk
        
        try:
            error_detail = json.loads(response_body_bytes.decode('utf-8'))
            app_specific_logger.info(f"VALIDATION ERROR DETAILS for {request.url.path}: {error_detail}")
        except Exception as e:
            app_specific_logger.warning(f"Could not parse 422 error response body for {request.url.path}: {e}. Body: {response_body_bytes[:500]}")

        return JSONResponse(
            status_code=response.status_code,
            content=json.loads(response_body_bytes.decode('utf-8')) if response_body_bytes else None,
            headers=dict(response.headers)
        )
    return response

# Mount static files (CSS, JS) if you have them
# app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates for HTML
templates = Jinja2Templates(directory=str(Path(__file__).parent / TEMPLATES_DIR))

# In-memory store for task statuses (for simplicity, replace with DB or Redis in production)
task_statuses: Dict[str, Dict[str, Any]] = {}

def get_current_timestamp():
    return datetime.datetime.now(datetime.timezone.utc).isoformat()

# --- Models ---
class BaseProcessingParams(BaseModel):
    enable_literal_translation: bool = Field(False, description="Enable literal translation for AI processing.")
    enable_decomposition: bool = Field(False, description="Enable translation decomposition for AI processing.")
    skip_scoring: bool = Field(False, description="Skip the final scoring step.")
    column_mappings: Optional[Dict[str, Optional[str]]] = Field(None, description="User-defined column mappings from Excel headers to logical fields. e.g., {'source_text': 'Excel Header for Original'}")
    # Add other CLI params as needed, e.g., column mappings if dynamic in future

class InitialProcessingPayload(BaseModel):
    file_id: str = Field(..., description="The ID of the uploaded file (from /upload/ endpoint).")
    filename: str = Field(..., description="The original filename of the uploaded file.")
    mode: Literal["simple", "professional"] = Field("simple", description="Processing mode: 'simple' for one-shot, 'professional' for step-by-step.")
    params: BaseProcessingParams = Field(..., description="Parameters for processing.")

class ProStepPayload(BaseModel):
    file_id: Optional[str] = Field(None, description="ID of a new file uploaded specifically for this step.")
    filename: Optional[str] = Field(None, description="Original filename of the new file for this step.")
    params: Optional[Dict[str, Any]] = Field(None, description="Step-specific parameters, e.g., AI model choices for AI step.")

# --- Helper Functions ---
async def run_docker_command(task_id: str, input_file_host_path: str, output_dir_host_path: str, params: BaseProcessingParams):
    task_info = task_statuses.get(task_id) 
    if not task_info: 
        app_specific_logger.error(f"Simple mode: Task {task_id} not found in task_statuses at start of run_docker_command.")
        return

    # Update task_info for docker run
    task_info.update({
        "task_id": task_id, # Already present, but good to ensure
        "status": "running_docker", 
        "mode": "simple",
        "log": task_info.get("log", ""), 
        "output_files": [], 
        "error_message": None, 
        "created_at": task_info.get("created_at", get_current_timestamp()),
        "updated_at": get_current_timestamp(),
        "original_filename": Path(input_file_host_path).name, 
        "step_outputs": task_info.get("step_outputs", { 
            "original": {"host_path": str(input_file_host_path), "status": "completed", "display_name": Path(input_file_host_path).name }
        }),
        "user_selected_params": params.model_dump(),
        # Initialize new fields for consistency, though not primarily used by simple mode in this way
        "step_specific_inputs": task_info.get("step_specific_inputs", {}),
        "ai_progress": task_info.get("ai_progress", None) 
    })
    
    host_project_dir_str = os.getenv("HOST_PROJECT_DIR")
    if not host_project_dir_str:
        app_specific_logger.error("HOST_PROJECT_DIR environment variable is not set.")
        task_info["status"] = "failed"
        task_info["error"] = "Server configuration error: HOST_PROJECT_DIR not set."
        return
    host_project_dir = Path(host_project_dir_str)

    base_input_filename = Path(input_file_host_path).name
    host_side_input_path = host_project_dir / "web_server" / UPLOADS_DIR / base_input_filename
    host_side_output_path_for_cli = host_project_dir / "web_server" / OUTPUTS_DIR / task_id
    Path(host_side_output_path_for_cli).mkdir(parents=True, exist_ok=True)

    container_input_excel = "/usr/src/app/mounted_input.xlsx"
    container_output_dir = "/usr/src/app/cli_output"

    qwen_api_key = os.getenv("QWEN_API_KEY", "your_qwen_api_key_placeholder") 
    deepseek_api_key = os.getenv("DEEPSEEK_API_KEY", "your_deepseek_api_key_placeholder")

    cmd = [
        "docker", "run", "--rm",
        "-v", f"{str(host_side_input_path)}:{container_input_excel}",
        "-v", f"{str(host_side_output_path_for_cli)}:{container_output_dir}",
        "auto-check-cli", 
        "--input_excel", container_input_excel,
        "--output_dir", container_output_dir,
        "--qwen_api_key", qwen_api_key,
        "--deepseek_api_key", deepseek_api_key
    ]
    if params.enable_literal_translation:
        cmd.append("--enable_literal_translation")
    if params.enable_decomposition:
        cmd.append("--enable_decomposition")
    if params.skip_scoring:
        cmd.append("--skip_scoring")

    log_file_path = LOG_DIR_PATH / f"{task_id}_simple_docker_execution.log"
    full_log = ""

    try:
        task_info["log"] += f"Executing command: {' '.join(cmd)}\n"
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        async def log_stream(stream, stream_name):
            nonlocal full_log
            async for line_bytes in stream:
                line = line_bytes.decode('utf-8', errors='replace').strip()
                log_entry = f"[{stream_name}] {line}\n"
                full_log += log_entry
                if task_id in task_statuses and task_statuses[task_id]["mode"] == "simple":
                    task_statuses[task_id]["log"] += log_entry
        
        await asyncio.gather(log_stream(process.stdout, "stdout"), log_stream(process.stderr, "stderr"))
        return_code = await process.wait()

        if return_code == 0:
            task_info["status"] = "completed" # Simple mode directly to completed
            # Discover output files from host_side_output_path_for_cli for simple mode
            discovered_outputs = []
            for item in host_side_output_path_for_cli.iterdir():
                if item.is_file():
                    discovered_outputs.append(item.name)
            task_info["output_files"] = discovered_outputs 
            task_info["log"] += "\nProcessing completed successfully via Docker."
        else:
            task_info["status"] = "failed"
            task_info["error"] = f"Docker process exited with code {return_code}."
        task_info["updated_at"] = get_current_timestamp()

    except FileNotFoundError:
        app_specific_logger.error("Docker command not found. Is Docker installed and in PATH?")
        task_info["status"] = "failed"
        task_info["error"] = "Docker command not found."
    except Exception as e:
        app_specific_logger.error(f"Error running Docker command for task {task_id}: {e}", exc_info=True)
        task_info["status"] = "failed"
        task_info["error"] = str(e)
    finally:
        with open(log_file_path, 'w', encoding='utf-8') as f_log:
            f_log.write(full_log)
        if task_id in task_statuses:
            task_statuses[task_id]["log_file"] = log_file_path.name 
            task_statuses[task_id]["updated_at"] = get_current_timestamp()


# --- Background task for professional mode steps (NEW) ---
async def execute_professional_step(task_id: str):
    task_info = task_statuses.get(task_id)
    if not task_info:
        app_specific_logger.error(f"Professional Task {task_id} not found in execute_professional_step")
        return

    try:
        from app import config as g_app_config 
        from app.preprocess import preprocess_data_core_adapted
        from app.rich_text_checker import run_rich_text_check_core
        from app.translation_validator import run_validation_batch 
        from app.translation_scorer import run_translation_scoring_core
        from app.main_cli import prepare_validation_tasks 
    except ImportError as e:
        task_info["status"] = "failed"
        task_info["error_message"] = f"Critical: Failed to import app modules for step execution: {e}."
        app_specific_logger.critical(f"Task {task_id}: Import error for app modules - {e}", exc_info=True)
        task_info["updated_at"] = get_current_timestamp()
        return
    
    current_step_name = task_info.get("current_step_name") 
    if not current_step_name:
        app_specific_logger.error(f"Task {task_id}: execute_professional_step called but current_step_name is missing.")
        task_info["status"] = "failed"
        task_info["error_message"] = "Internal error: Step to execute is not defined."
        task_info["updated_at"] = get_current_timestamp()
        return

    app_specific_logger.info(f"Task {task_id}: Starting execution of professional step: '{current_step_name}'")
    task_info["status"] = "running_step" 
    if current_step_name in task_info.get("step_outputs", {}):
         task_info["step_outputs"][current_step_name]["status"] = "running"
    else: 
        app_specific_logger.warning(f"Task {task_id}: '{current_step_name}' not found in step_outputs during run. Initializing.")
        task_info.setdefault("step_outputs", {}).setdefault(current_step_name, {
            "status": "running", "host_path": None, "internal_path": None, 
            "display_name": f"{current_step_name}_output", "stats": None, "error": None
        })
    task_info["updated_at"] = get_current_timestamp()

    task_output_dir = OUTPUTS_DIR / task_id
    user_selected_params = task_info.get("user_selected_params", {})
    column_mappings_to_use = user_selected_params.get("column_mappings") or g_app_config.DEFAULT_COLUMN_MAPPINGS_PREPROCESS

    current_input_path_str = None
    output_file_path_obj = None 
    output_file_display_name = task_info["step_outputs"][current_step_name].get("display_name", f"{current_step_name}_output")
    step_execution_stats = None
    step_execution_error = None

    step_specific_input_info = task_info.get('step_specific_inputs', {}).get(current_step_name)
    if step_specific_input_info and step_specific_input_info.get('path'):
        current_input_path_str = step_specific_input_info['path']
        app_specific_logger.info(f"Task {task_id}, Step '{current_step_name}': Using step-specific input file: '{current_input_path_str}'")
    else:
        if current_step_name == 'preprocess':
            current_input_path_str = task_info.get('initial_file_path')
            app_specific_logger.info(f"Task {task_id}, Step '{current_step_name}': Using initial task file: '{current_input_path_str}'")
        elif current_step_name == 'richtext':
            preprocess_step_output = task_info.get('step_outputs', {}).get('preprocess', {})
            if preprocess_step_output.get('status') == 'completed': current_input_path_str = preprocess_step_output.get('internal_path')
        elif current_step_name == 'ai_processing':
            preprocess_step_output_for_ai = task_info.get('step_outputs', {}).get('preprocess', {})
            if preprocess_step_output_for_ai.get('status') == 'completed': current_input_path_str = preprocess_step_output_for_ai.get('internal_path')
        elif current_step_name == 'scoring':
            ai_processing_step_output = task_info.get('step_outputs', {}).get('ai_processing', {})
            if ai_processing_step_output.get('status') == 'completed': current_input_path_str = ai_processing_step_output.get('internal_path')
    
    if not current_input_path_str or not Path(current_input_path_str).exists():
        step_execution_error = f"Input file for step '{current_step_name}' not found or not accessible: {current_input_path_str}"
        app_specific_logger.error(f"Task {task_id}: {step_execution_error}")
    else:
        current_input_path_obj = Path(current_input_path_str)
        app_specific_logger.info(f"Task {task_id}, Step '{current_step_name}': Determined input file: '{current_input_path_obj}'")
        try:
            # 2. Execute step-specific logic
            if current_step_name == "preprocess":
                output_file_path_obj = task_output_dir / task_info["step_outputs"]["preprocess"]["display_name"]
                processed_data_dict = preprocess_data_core_adapted(
                    str(current_input_path_obj),
                    column_mappings=column_mappings_to_use,
                    file_name_mapping_dict=g_app_config.FILE_NAME_MAPPING_PREPROCESS,
                    expected_columns_config=g_app_config.EXPECTED_COLUMNS_CONFIG_PREPROCESS
                )
                if processed_data_dict is None: raise ValueError("Preprocessing returned no data (None).")
                with open(output_file_path_obj, 'w', encoding='utf-8') as f: json.dump(processed_data_dict, f, ensure_ascii=False, indent=2)
                step_execution_stats = {"records_processed": len(processed_data_dict)}

            elif current_step_name == "richtext":
                output_file_path_obj = task_output_dir / task_info["step_outputs"]["richtext"]["display_name"]
                with open(current_input_path_obj, 'r', encoding='utf-8') as f_json: json_data_str = f_json.read()
                df_results, rt_stats = run_rich_text_check_core(
                    json_data_str,
                    g_app_config.DEFAULT_PREPROCESS_ORIGINAL_TEXT_KEY,
                    g_app_config.DEFAULT_PREPROCESS_TRANSLATED_TEXT_KEY
                )
                df_results.to_excel(output_file_path_obj, index=False)
                step_execution_stats = rt_stats

            elif current_step_name == "ai_processing":
                output_file_path_obj = task_output_dir / task_info["step_outputs"]["ai_processing"]["display_name"]
                with open(current_input_path_obj, 'r', encoding='utf-8') as f_ai_json: processed_data_dict_for_ai = json.load(f_ai_json)
                tasks_to_run_ai = prepare_validation_tasks(processed_data_dict_for_ai)
                
                task_info['ai_progress'] = {'completed': 0, 'total': len(tasks_to_run_ai) if tasks_to_run_ai else 0, 'error': None}
                app_specific_logger.info(f"Task {task_id} '{current_step_name}': AI progress initialized: {task_info['ai_progress']}")

                if not tasks_to_run_ai:
                    app_specific_logger.info(f"Task {task_id} '{current_step_name}': No tasks for AI. Marking step as skipped.")
                    task_info["step_outputs"][current_step_name]["status"] = "skipped"
                    step_execution_stats = {"message": "No items for AI processing."}
                    output_file_path_obj = None 
                else:
                    qwen_api_key = os.getenv("QWEN_API_KEY")
                    deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
                    if not qwen_api_key or not deepseek_api_key:
                         app_specific_logger.warning(f"Task {task_id} '{current_step_name}': API keys for Qwen or DeepSeek are missing. AI processing might fail or use placeholders.")
                    
                    ai_excel_rows = run_validation_batch(
                        tasks_to_run=tasks_to_run_ai,
                        qwen_model_name=os.getenv("QWEN_VALIDATOR_MODEL", g_app_config.QWEN3_MODEL_VALIDATOR),
                        deepseek_model_name=os.getenv("DEEPSEEK_VALIDATOR_MODEL", g_app_config.DEEPSEEK_MODEL_VALIDATOR),
                        qwen_api_key=qwen_api_key,
                        deepseek_api_key=deepseek_api_key,
                        qwen_base_url=os.getenv("QWEN_VALIDATOR_BASE_URL", g_app_config.QWEN3_BASE_URL_VALIDATOR),
                        deepseek_base_url=os.getenv("DEEPSEEK_VALIDATOR_BASE_URL", g_app_config.DEEPSEEK_BASE_URL_VALIDATOR),
                        enable_literal_translation=user_selected_params.get('enable_literal_translation', False),
                        literal_translator_model_name=os.getenv("LITERAL_TRANSLATOR_MODEL", g_app_config.LITERAL_TRANSLATOR_MODEL),
                        literal_translator_base_url=os.getenv("LITERAL_TRANSLATOR_BASE_URL", g_app_config.LITERAL_TRANSLATOR_BASE_URL),
                        enable_decomposition=user_selected_params.get('enable_decomposition', False),
                        decomposition_model_name=os.getenv("DECOMPOSITION_MODEL", g_app_config.DECOMPOSITION_MODEL),
                        decomposition_base_url=os.getenv("DECOMPOSITION_BASE_URL", g_app_config.DECOMPOSITION_BASE_URL),
                        max_workers=int(os.getenv("VALIDATOR_MAX_WORKERS", g_app_config.VALIDATOR_MAX_WORKERS_DEFAULT))
                    )
                    if ai_excel_rows is None: raise ValueError("AI validation batch returned None, expected a list.")
                    df_ai_results = pd.DataFrame(ai_excel_rows)
                    df_ai_results.to_excel(output_file_path_obj, index=False)
                    task_info['ai_progress']['completed'] = task_info['ai_progress']['total'] 
                    step_execution_stats = task_info['ai_progress']
            
            elif current_step_name == "scoring":
                output_file_path_obj = task_output_dir / task_info["step_outputs"]["scoring"]["display_name"]
                app_specific_logger.info(f"Task {task_id} '{current_step_name}': Input '{current_input_path_obj}', Output '{output_file_path_obj}'")
                df_to_score = pd.read_excel(current_input_path_obj)
                scored_df_results, scoring_stats_map = run_translation_scoring_core(df_to_score)
                scored_df_results.to_excel(output_file_path_obj, index=False)
                step_execution_stats = scoring_stats_map
            else:
                raise ValueError(f"Unknown professional step for execution logic: '{current_step_name}'")
            
            # 3. Update step output info upon successful execution (if not skipped)
            if task_info["step_outputs"].get(current_step_name, {}).get("status") != "skipped":
                task_info["step_outputs"][current_step_name].update({
                    "status": "completed",
                    "host_path": str(output_file_path_obj.resolve()) if output_file_path_obj else None,
                    "internal_path": str(output_file_path_obj) if output_file_path_obj else None,
                    "stats": step_execution_stats,
                    "error": None
                })
            
            # Pre-calculate the status string for logging to avoid f-string issues
            step_final_status_for_log = task_info["step_outputs"][current_step_name].get("status", "Unknown Status")
            current_step_name_for_log = str(current_step_name) # Ensure it's a string

            app_specific_logger.info(f"Task {task_id}: Step '{current_step_name_for_log}' processing finished. Status: {step_final_status_for_log}")
            _update_professional_task_flow(task_id, current_step_name, True)

        except Exception as e_step_exec:
            step_execution_error = f"Error during step '{current_step_name}': {str(e_step_exec)}"
            app_specific_logger.error(f"Task {task_id}: {step_execution_error}", exc_info=True)
    
    # 4. Final status update for the step if any error occurred (either input error or execution error)
    if step_execution_error:
        task_info["step_outputs"].setdefault(current_step_name, {}).update({
            "status": "failed", 
            "error": step_execution_error,
            "stats": step_execution_stats, # May have partial stats or None
            # Keep existing host_path/internal_path/display_name if they were set and error happened late
            "host_path": task_info["step_outputs"].get(current_step_name,{}).get("host_path", str(output_file_path_obj.resolve()) if output_file_path_obj and output_file_path_obj.exists() else None),
            "internal_path": task_info["step_outputs"].get(current_step_name,{}).get("internal_path", str(output_file_path_obj) if output_file_path_obj else None),
            "display_name": output_file_display_name # Ensure display_name is preserved or set
        })
        if current_step_name == "ai_processing" and task_info.get('ai_progress'): # Also mark error in AI progress
            task_info['ai_progress']['error'] = step_execution_error
        _update_professional_task_flow(task_id, current_step_name, False) # This will set overall task to failed

    task_info["updated_at"] = get_current_timestamp()

def _update_professional_task_flow(task_id: str, completed_step_key: str, success: bool):
    task_info = task_statuses.get(task_id)
    if not task_info: return

    app_specific_logger.info(f"Task {task_id}: Updating flow. Step '{completed_step_key}' ended with success: {success}")
    task_info["current_step_name"] = None 

    if not success:
        task_info["status"] = "failed" 
        task_info["next_step_name"] = None 
        # Prepend error message to avoid losing previous context if multiple steps fail or are retried
        existing_error = task_info.get("error_message", "")
        new_error = f"Error in step: {completed_step_key}. "
        task_info["error_message"] = (new_error + existing_error) if existing_error else new_error
        app_specific_logger.info(f"Task {task_id}: Marked as failed. Error: {task_info['error_message']}")
        return

    professional_step_order = ["preprocess", "richtext", "ai_processing", "scoring"]
    try:
        current_index = professional_step_order.index(completed_step_key)
    except ValueError:
        task_info["status"] = "failed"
        task_info["error_message"] = f"Internal error: Completed step '{completed_step_key}' is not in defined order."
        task_info["next_step_name"] = None
        app_specific_logger.error(f"Task {task_id}: {task_info['error_message']}")
        return

    user_params = task_info.get("user_selected_params", {})
    # Check if the current completed step was AI processing and if scoring should be skipped
    if completed_step_key == "ai_processing" and user_params.get("skip_scoring", False):
        app_specific_logger.info(f"Task {task_id}: Scoring step will be skipped as per user configuration after AI processing.")
        # Mark scoring step as skipped in its output entry
        task_info["step_outputs"]["scoring"] = task_info["step_outputs"].get("scoring", {})
        task_info["step_outputs"]["scoring"].update({"status": "skipped", "host_path": None, "internal_path":None, "display_name": "翻译评分 (已跳过)", "stats": None, "error": None})
        
        task_info["status"] = "completed" # All relevant steps are done
        task_info["next_step_name"] = None
        app_specific_logger.info(f"Task {task_id}: All professional steps completed (scoring skipped).")
    elif current_index + 1 < len(professional_step_order):
        next_step = professional_step_order[current_index + 1]
        task_info["status"] = "pending_next_step"
        task_info["next_step_name"] = next_step
        app_specific_logger.info(f"Task {task_id}: Step '{completed_step_key}' successful. Next step is '{next_step}'.")
    else:
        task_info["status"] = "completed" 
        task_info["next_step_name"] = None
        app_specific_logger.info(f"Task {task_id}: All professional steps completed successfully.")
    
    task_info["updated_at"] = get_current_timestamp()


# --- FastAPI Endpoints ---
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/upload/")
async def upload_excel_file(file: UploadFile = File(...)):
    # 允许 .xlsx, .xls, .json 文件
    allowed_extensions = (".xlsx", ".xls", ".json")
    if not file.filename.endswith(allowed_extensions):
        raise HTTPException(status_code=400, detail=f"Invalid file type. Allowed: {', '.join(allowed_extensions)}")
    
    file_id = str(uuid.uuid4()) # Unique ID for this specific upload instance
    original_filename = file.filename 
    
    # Store each upload in its own directory named by file_id to prevent filename clashes
    # and to associate it with this specific upload event.
    upload_dir_for_file = UPLOADS_DIR / file_id
    upload_dir_for_file.mkdir(parents=True, exist_ok=True)
    saved_file_path = upload_dir_for_file / original_filename

    headers = []
    try:
        # Save the file first
        with open(saved_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer) # Corrected: Use file.file for UploadFile object
        app_specific_logger.info(f"File uploaded: '{original_filename}' (upload_id: {file_id}) to '{saved_file_path}'")

        # If it's an Excel file, try to read headers
        if original_filename.endswith((".xlsx", ".xls")):
            try:
                df_headers = pd.read_excel(saved_file_path, nrows=0, sheet_name=0) 
                headers = df_headers.columns.tolist()
                app_specific_logger.info(f"Extracted headers from '{original_filename}': {headers}")
            except Exception as e_header:
                app_specific_logger.warning(f"Could not read headers from Excel '{original_filename}' (upload_id: {file_id}): {e_header}")
                # Proceed with empty headers, frontend can inform user

    except Exception as e_upload:
        app_specific_logger.error(f"Error during file upload or header reading for '{original_filename}' (upload_id: {file_id}): {e_upload}", exc_info=True)
        # Attempt to clean up if file was partially saved
        if saved_file_path.exists():
            try:
                os.remove(saved_file_path)
                app_specific_logger.info(f"Cleaned up partially uploaded file: {saved_file_path}")
            except OSError as ose:
                app_specific_logger.error(f"Error cleaning up file {saved_file_path} after failed upload: {ose}")
        raise HTTPException(status_code=500, detail=f"Could not process file upload: {e_upload}")
    finally:
        if hasattr(file, 'file') and file.file and not file.file.closed:
            file.file.close()
        
    return {
        "message": "File uploaded successfully", 
        "file_id": file_id, # This is the ID of the upload instance
        "filename": original_filename, 
        "saved_path": str(saved_file_path.resolve()), # Return absolute path for backend internal use
        "headers": headers  
    }

@app.post("/initiate_processing/")
async def initiate_processing_task(payload: InitialProcessingPayload, background_tasks: BackgroundTasks):
    task_id = str(uuid.uuid4())
    app_specific_logger.info(f"Initiating task {task_id} with payload: {payload.dict()}")
    source_upload_path = UPLOADS_DIR / payload.file_id / payload.filename
    if not source_upload_path.exists():
        app_specific_logger.error(f"Task {task_id} initiation: Source uploaded file not found at {source_upload_path}")
        raise HTTPException(status_code=404, detail=f"Uploaded file '{payload.filename}' (ID: {payload.file_id}) not found. Please upload again.")
    task_output_dir = OUTPUTS_DIR / task_id
    task_output_dir.mkdir(parents=True, exist_ok=True)
    task_initial_input_file_path = task_output_dir / payload.filename 
    try:
        shutil.copy(source_upload_path, task_initial_input_file_path)
        app_specific_logger.info(f"Task {task_id}: Copied initial file from {source_upload_path} to {task_initial_input_file_path}")
    except Exception as e_copy:
        app_specific_logger.error(f"Task {task_id} initiation: Failed to copy {source_upload_path} to {task_initial_input_file_path}: {e_copy}")
        raise HTTPException(status_code=500, detail="Failed to prepare task resources.")
    step_outputs_template = {
        "original": {"host_path": str(task_initial_input_file_path.resolve()), "internal_path": str(task_initial_input_file_path), "display_name": payload.filename, "status": "completed", "stats": None, "error": None},
        "preprocess": {"host_path": None, "internal_path": None, "display_name": "preprocessed_output.json", "status": "pending", "stats": None, "error": None},
        "richtext": {"host_path": None, "internal_path": None, "display_name": "richtext_report.xlsx", "status": "pending", "stats": None, "error": None},
        "ai_processing": {"host_path": None, "internal_path": None, "display_name": "ai_processing_report.xlsx", "status": "pending", "stats": None, "error": None},
        "scoring": {"host_path": None, "internal_path": None, "display_name": "final_scoring_report.xlsx", "status": "pending", "stats": None, "error": None}
    }
    task_statuses[task_id] = {
        "task_id": task_id,
        "mode": payload.mode,
        "initial_file_id": payload.file_id,
        "initial_filename": payload.filename,
        "initial_file_path": str(task_initial_input_file_path.resolve()),
        "user_selected_params": payload.params.model_dump(),
        "status": "pending_next_step" if payload.mode == "professional" else "queued_docker",
        "current_step_name": None,
        "next_step_name": "preprocess" if payload.mode == "professional" else None,
        "error_message": None,
        "created_at": get_current_timestamp(),
        "updated_at": get_current_timestamp(),
        "step_outputs": step_outputs_template,
        "step_specific_inputs": {}, 
        "ai_progress": None, 
        "log_file": None 
    }
    app_specific_logger.info(f"Task {task_id} fully initialized in task_statuses. Mode: {payload.mode}")
    if payload.mode == "simple":
        app_specific_logger.info(f"Task {task_id}: Queuing simple mode processing via Docker.")
        background_tasks.add_task(run_docker_command, task_id, str(task_initial_input_file_path), str(task_output_dir), payload.params)
    elif payload.mode == "professional":
        app_specific_logger.info(f"Task {task_id}: Professional mode initiated. Ready for user to trigger 'preprocess' step.")
    return {"task_id": task_id, "message": "Processing initiated successfully."}

@app.post("/process/{task_id}/next_step")
async def process_professional_next_step(
    task_id: str, 
    background_tasks: BackgroundTasks,
    payload: Optional[ProStepPayload] = Body(None) # Payload is optional for triggering next step
):
    task_info = task_statuses.get(task_id)
    if not task_info:
        app_specific_logger.error(f"Next step request: Task {task_id} not found.")
        raise HTTPException(status_code=404, detail="Task not found")
    
    if task_info.get("mode") != "professional":
        app_specific_logger.warning(f"Next step request: Task {task_id} is not in professional mode.")
        raise HTTPException(status_code=400, detail="This endpoint is for professional mode tasks only.")

    current_overall_status = task_info.get("status")
    step_to_execute_now = task_info.get("next_step_name") # This is the step we intend to run

    if not step_to_execute_now:
        app_specific_logger.warning(f"Next step request for task {task_id}: No next_step_name defined (current status: {current_overall_status}). Task might be completed or failed.")
        if current_overall_status == "completed":
            return {"message": "Task already completed.", "task_id": task_id, "status": current_overall_status}
        elif current_overall_status == "failed":
             return {"message": f"Task has failed: {task_info.get('error_message', 'Unknown error')}. Cannot proceed.", "task_id": task_id, "status": current_overall_status}
        # If no next step and not completed/failed, it's an inconsistent state or user trying to advance prematurely
        raise HTTPException(status_code=400, detail=f"No next step defined or task in terminal/unexpected state (status: {current_overall_status}).")

    if current_overall_status not in ["pending_next_step"]:
        app_specific_logger.warning(f"Next step request for task {task_id}: Task is not in 'pending_next_step' state (current status: {current_overall_status}). Cannot start step '{step_to_execute_now}'.")
        raise HTTPException(status_code=400, detail=f"Task not ready for '{step_to_execute_now}'. Current status: {current_overall_status}")
    
    # Update task_info with new inputs/params from payload if provided
    if payload:
        app_specific_logger.info(f"Task {task_id}, Preparing to run step '{step_to_execute_now}': Received payload: {payload.model_dump(exclude_none=True)}")
        if payload.file_id and payload.filename:
            # User provided a specific file for this step
            uploaded_file_path_for_step = UPLOADS_DIR / payload.file_id / payload.filename
            if not uploaded_file_path_for_step.exists():
                app_specific_logger.error(f"Task {task_id}, Step '{step_to_execute_now}': Specified input file '{uploaded_file_path_for_step}' not found.")
                raise HTTPException(status_code=400, detail=f"Specified input file for step '{step_to_execute_now}' not found: {payload.filename}")
            
            task_info.setdefault('step_specific_inputs', {})[step_to_execute_now] = {
                'file_id': payload.file_id,
                'filename': payload.filename,
                'path': str(uploaded_file_path_for_step.resolve()) # Store absolute path as string
            }
            app_specific_logger.info(f"Task {task_id}, Step '{step_to_execute_now}': Set specific input file to '{uploaded_file_path_for_step}'.")
        
        if payload.params: # params in ProStepPayload is Dict[str, Any]
            task_info.setdefault('user_selected_params', {}).update(payload.params)
            app_specific_logger.info(f"Task {task_id}, Step '{step_to_execute_now}': Updated user_selected_params with {payload.params}.")

    # Set current_step_name to the step that is about to be executed
    task_info["current_step_name"] = step_to_execute_now
    task_info["next_step_name"] = None # This will be set by _update_professional_task_flow upon successful completion of current_step_name
    task_info["status"] = "queued_step" # Overall task status indicates a step is queued
    
    # Update status for the specific step being queued
    if step_to_execute_now in task_info.get("step_outputs", {}):
        task_info["step_outputs"][step_to_execute_now]["status"] = "queued"
        task_info["step_outputs"][step_to_execute_now]["error"] = None # Clear previous error for this step if retrying
        task_info["step_outputs"][step_to_execute_now]["stats"] = None # Clear previous stats
    else: # Should not happen if step_outputs is initialized correctly
        app_specific_logger.warning(f"Task {task_id}: Step '{step_to_execute_now}' not found in step_outputs during queuing. Re-initializing.")
        task_info.setdefault("step_outputs", {})[step_to_execute_now] = {"status": "queued", "error": None, "stats": None, "host_path": None, "internal_path":None, "display_name": f"{step_to_execute_now}_output"}


    app_specific_logger.info(f"Task {task_id}: Queuing professional step '{task_info['current_step_name']}'.")
    background_tasks.add_task(execute_professional_step, task_id) # execute_professional_step will use task_info["current_step_name"]
    
    return {
        "message": f"Step '{task_info['current_step_name']}' for task {task_id} has been initiated.", 
        "task_id": task_id, 
        "current_step_queued": task_info['current_step_name']
    }

@app.get("/status/{task_id}")
async def get_task_status_endpoint(task_id: str): 
    task_info = task_statuses.get(task_id)
    if not task_info:
        app_specific_logger.warning(f"Status request: Task {task_id} not found.")
        raise HTTPException(status_code=404, detail="Task not found")
    
    response_data = {
        "task_id": task_info.get("task_id"),
        "mode": task_info.get("mode"),
        "status": task_info.get("status"),
        "current_step_name": task_info.get("current_step_name"),
        "next_step_name": task_info.get("next_step_name"),
        "error_message": task_info.get("error_message"),
        "user_selected_params": task_info.get("user_selected_params"), 
        "initial_filename": task_info.get("initial_filename"),
        "updated_at": task_info.get("updated_at", task_info.get("created_at"))
    }

    if task_info.get("mode") == "simple":
        response_data["output_files"] = task_info.get("output_files", [])
        response_data["log_file"] = task_info.get("log_file") 
        response_data["log_content_snippet"] = task_info.get("log", "")[-1000:] 

    elif task_info.get("mode") == "professional":
        response_data["step_outputs"] = {}
        professional_step_order = ["original", "preprocess", "richtext", "ai_processing", "scoring"]
        for step_key in professional_step_order:
            step_data_from_task = task_info.get("step_outputs", {}).get(step_key, {})
            # Ensure all keys are present even if None, for frontend consistency
            response_data["step_outputs"][step_key] = {
                "status": step_data_from_task.get("status", "pending"),
                "host_path": step_data_from_task.get("host_path"), 
                "internal_path": step_data_from_task.get("internal_path"), # For debugging or internal reference
                "display_name": step_data_from_task.get("display_name"),
                "stats": step_data_from_task.get("stats"),
                "error": step_data_from_task.get("error")
            }
        response_data["ai_progress"] = task_info.get("ai_progress")

    app_specific_logger.debug(f"Task {task_id} status request response: {response_data}")
    return response_data

@app.get("/download/{task_id}/{step_name}", summary="Download an output file from a specific step")
async def download_output_file_step(task_id: str, step_name: str):
    if task_id not in task_statuses:
        raise HTTPException(status_code=404, detail="Task not found")
    task_info = task_statuses[task_id]

    if step_name not in task_info["step_outputs"]:
        raise HTTPException(status_code=404, detail=f"Step '{step_name}' not found for this task.")
    
    step_output_info = task_info["step_outputs"][step_name]
    if step_output_info["status"] != "completed" and step_output_info["status"] != "skipped": # Allow download if skipped but had a placeholder path that might exist
        # Or, if skipped means no file, then check host_path is not None
        if not (step_output_info["status"] == "skipped" and step_output_info["host_path"] is None):
             raise HTTPException(status_code=400, detail=f"Output for step '{step_name}' is not ready or the step was skipped without an output. Status: {step_output_info['status']}")

    file_path_str = step_output_info.get("host_path")
    if not file_path_str:
        raise HTTPException(status_code=404, detail=f"Output file path for step '{step_name}' is not available (e.g., step skipped or failed before output). ")

    file_path = Path(file_path_str)
    if not file_path.is_file():
        app_specific_logger.error(f"Download error: File {file_path} not found on server for task {task_id}, step {step_name}.")
        raise HTTPException(status_code=404, detail="File not found on server.")
    
    return FileResponse(path=file_path, filename=step_output_info.get("display_name", file_path.name))

@app.get("/download_log/{task_id}", summary="Download the log file for a task")
async def download_log_file(task_id: str):
    if task_id not in task_statuses:
        raise HTTPException(status_code=404, detail="Task not found")
    task_info = task_statuses[task_id]
    log_file_name = task_info.get("log_file") # This is set by run_docker_command for simple mode
    
    # For professional mode, logs are currently just stdout. We might want to save them to a file too.
    # For now, this endpoint primarily serves simple mode logs.
    if not log_file_name:
        # Attempt to construct a log path for professional mode if needed in future
        # prof_log_path = LOG_DIR_PATH / f"{task_id}_prof.log"
        # if prof_log_path.exists(): log_file_path = prof_log_path else: ...
        raise HTTPException(status_code=404, detail="Log file not available for this task or mode.")
    
    log_file_path = LOG_DIR_PATH / log_file_name
    if not log_file_path.is_file():
        raise HTTPException(status_code=404, detail="Log file not found on server.")
    
    return FileResponse(path=log_file_path, filename=log_file_name)

@app.get("/download_simple_output/{task_id}/{filename}", summary="Download an output file from a simple mode task")
async def download_simple_output_file(task_id: str, filename: str):
    if task_id not in task_statuses:
        raise HTTPException(status_code=404, detail="Task not found")
    task_info = task_statuses[task_id]

    if task_info["mode"] != "simple":
        raise HTTPException(status_code=400, detail="This download endpoint is for simple mode tasks only.")

    if task_info["status"] != "completed":
        raise HTTPException(status_code=400, detail=f"Simple mode task is not completed yet. Current status: {task_info['status']}")

    # Files from simple mode (docker output) are expected in <OUTPUTS_DIR>/<task_id>/cli_output/<filename>
    # This is because run_docker_command maps the host's <OUTPUTS_DIR>/<task_id> 
    # to the auto-check-cli container's /usr/src/app/cli_output directory.
    # So, the files listed in task_info["output_files"] are relative to that cli_output dir.
    
    # It seems task_info["output_files"] stores just the filename, not path relative to cli_output.
    # We need to ensure filename is one of the discovered output_files for safety, though direct path construction is used.
    # if filename not in task_info.get("output_files", []):
    #     raise HTTPException(status_code=404, detail=f"File '{filename}' not listed in task outputs.")

    file_path = Path(OUTPUTS_DIR) / task_id / "cli_output" / filename
    
    if not file_path.is_file():
        app_specific_logger.error(f"Download error (simple mode): File {file_path} not found on server for task {task_id}.")
        # Double check if the filename might have been stored with a path prefix in output_files
        # For now, assume filename is just the basename.
        raise HTTPException(status_code=404, detail=f"File '{filename}' not found on server within the task's output directory.")
    
    return FileResponse(path=file_path, filename=filename)

@app.get("/get_main_log/", response_class=PlainTextResponse)
async def get_main_application_log_endpoint(): 
    try:
        if LOG_FILE_WEB_APP.exists():
            MAX_LOG_TAIL_BYTES = 1 * 1024 * 1024  # 1MB
            log_size = LOG_FILE_WEB_APP.stat().st_size
            content = ""
            with open(LOG_FILE_WEB_APP, "rb") as f: 
                if log_size > MAX_LOG_TAIL_BYTES:
                    f.seek(log_size - MAX_LOG_TAIL_BYTES)
                    content = f"... (Log truncated - showing last {MAX_LOG_TAIL_BYTES // 1024}KB of {log_size // 1024}KB) ...\n\n"
                content += f.read().decode('utf-8', errors='replace')
            app_specific_logger.info(f"Serving main application log content requested by user.") 
            return PlainTextResponse(content)
        else:
            app_specific_logger.warning(f"Main application log file {LOG_FILE_WEB_APP} not found when requested.")
            return PlainTextResponse("Application log file not found.", status_code=404)
    except Exception as e:
        app_specific_logger.error(f"Error reading main application log: {e}", exc_info=True)
        return PlainTextResponse(f"Error reading application log: {str(e)}", status_code=500)

if __name__ == "__main__":
    import uvicorn
    # It's better to run uvicorn from the command line: uvicorn web_server.main_web:app --reload
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="warning") 