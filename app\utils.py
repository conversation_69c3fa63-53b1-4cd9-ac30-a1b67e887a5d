import pandas as pd
import io
import os
import json
import logging
from typing import Union

logger = logging.getLogger(__name__) # 每个模块使用自己的logger是一个好习惯

def save_dataframe_to_excel(df: pd.DataFrame, output_dir: str, file_name: str) -> Union[str, None]:
    """将DataFrame保存到指定目录的Excel文件。"""
    if not isinstance(df, pd.DataFrame):
        logger.error(f"保存Excel失败：提供的数据不是有效的DataFrame。文件名: {file_name}")
        return None
    if df.empty:
        logger.info(f"DataFrame为空，不保存文件: {file_name}")
        return None

    output_path = os.path.join(output_dir, file_name)
    try:
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)
        logger.info(f"DataFrame成功保存到: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"保存DataFrame到Excel失败 ({output_path}): {e}")
        return None

def save_json_string_to_file(json_str: str, output_dir: str, file_name: str) -> Union[str, None]:
    """将JSON字符串保存到指定目录的文件。"""
    if not isinstance(json_str, str):
        logger.error(f"保存JSON失败：提供的数据不是字符串。文件名: {file_name}")
        return None
    
    output_path = os.path.join(output_dir, file_name)
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            # 为了确保格式美观，如果它是有效的JSON字符串，可以先解析再dump
            try:
                parsed_json = json.loads(json_str)
                json.dump(parsed_json, f, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                f.write(json_str) # 如果不是标准JSON，直接写入
        logger.info(f"JSON数据成功保存到: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"保存JSON字符串到文件失败 ({output_path}): {e}")
        return None

def save_bytes_to_file(data_bytes: bytes, output_dir: str, file_name: str) -> Union[str, None]:
    """将字节数据保存到指定目录的文件。"""
    if not isinstance(data_bytes, bytes):
        logger.error(f"保存字节数据失败：提供的数据不是bytes类型。文件名: {file_name}")
        return None

    output_path = os.path.join(output_dir, file_name)
    try:
        with open(output_path, 'wb') as f:
            f.write(data_bytes)
        logger.info(f"字节数据成功保存到: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"保存字节数据到文件失败 ({output_path}): {e}")
        return None

# 移除了 Streamlit 特定的函数:
# - make_download_button
# - complete_step_and_proceed
# - fail_step 