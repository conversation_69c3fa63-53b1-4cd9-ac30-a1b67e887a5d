import time
import logging
from openai import OpenAI, APIStatusError, APITimeoutError
from .config import DECOMPOSITION_PROMPT, DECOMPOSITION_MAX_RETRIES, OPENAI_REQUEST_TIMEOUT_SECONDS

logger = logging.getLogger(__name__)

def explain_translation_text_decomposition_adapted(api_client: OpenAI, model_name: str, translation_text: str, retry_count: int = 0) -> str:
    try:
        response = api_client.chat.completions.create(
            model=model_name, 
            messages=[
                {"role": "system", "content": DECOMPOSITION_PROMPT},
                {"role": "user", "content": f"请解释这段文本: {translation_text}"}
            ],
            temperature=0.1, 
            stream=True,
            timeout=OPENAI_REQUEST_TIMEOUT_SECONDS
        )
        explanation_chunks = []
        for chunk in response:
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                explanation_chunks.append(chunk.choices[0].delta.content)
        explanation = ''.join(explanation_chunks).strip()
        return explanation

    except APITimeoutError as e:
        if retry_count < DECOMPOSITION_MAX_RETRIES: 
            logger.warning(f"Decomposer: API timeout for '{translation_text[:30]}...' (Attempt {retry_count + 1}/{DECOMPOSITION_MAX_RETRIES}). Retrying in {1 + retry_count}s. Error: {e}")
            time.sleep(1 + retry_count)
            return explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count + 1)
        else: 
            error_msg = f"翻译拆解错误: API调用超时 (已重试 {DECOMPOSITION_MAX_RETRIES} 次) - {str(e)[:100]}"
            logger.error(f"Decomposer: {error_msg} for '{translation_text[:30]}...'")
            return error_msg

    except APIStatusError as e:
        status_code = e.status_code
        error_detail = f"HTTP {status_code}: {e.message}"
        log_message = f"Decomposer: APIStatusError for '{translation_text[:30]}...' (Attempt {retry_count + 1}). Details: {error_detail}"

        if status_code == 401: 
            error_detail = f"HTTP {status_code} (认证失败): {e.message}"
        elif status_code == 429:
            error_detail = f"HTTP {status_code} (请求超限): {e.message}"
            if retry_count < DECOMPOSITION_MAX_RETRIES:
                logger.warning(f"Decomposer: API rate limit for '{translation_text[:30]}...' (Attempt {retry_count + 1}/{DECOMPOSITION_MAX_RETRIES}). Retrying in {1 + retry_count}s. Error: {e}")
                time.sleep(1 + retry_count) 
                return explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count + 1)
        elif status_code >= 500: 
            error_detail = f"HTTP {status_code} (服务器错误): {e.message}"
        
        logger.error(log_message)
        error_msg = f"翻译拆解错误: API调用失败 ({error_detail[:150]}) (已重试 {retry_count} 次)"
        return error_msg

    except Exception as e:
        logger.error(f"Decomposer: Unexpected error for '{translation_text[:30]}...' (Attempt {retry_count + 1}). Error: {e}", exc_info=True)
        if retry_count < DECOMPOSITION_MAX_RETRIES: 
            time.sleep(1 + retry_count)
            return explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count + 1)
        else: 
            error_msg = f"翻译拆解错误: API调用失败 (已重试 {DECOMPOSITION_MAX_RETRIES} 次) - {str(e)[:100]}"
            logger.error(f"Decomposer: Max retries reached for '{translation_text[:30]}...'. Final error: {e}")
            return error_msg
