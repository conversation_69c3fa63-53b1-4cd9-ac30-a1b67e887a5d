import streamlit as st
import pandas as pd
import json

def preprocess_data_core_adapted(uploaded_file_obj, column_mappings, file_name_mapping_dict, expected_columns_config_local):
    try:
        df = pd.read_excel(uploaded_file_obj, sheet_name='Sheet0')
    except FileNotFoundError:
        st.error(f"Input file not found (this error should ideally not occur with uploaded files).")
        return None
    except Exception as e:
        st.error(f"Error reading Excel file: {e}")
        return None

    for logical_key, config in expected_columns_config_local.items():
        if config.get("is_core", False) and logical_key not in column_mappings:
            st.error(f"Core field '{config['description']}' ({logical_key}) not mapped. Please check column mapping configuration.")
            return None
        if config.get("is_core", False) and not column_mappings.get(logical_key):
            st.error(f"Core field '{config['description']}' ({logical_key}) mapped to an empty column name. Please select a valid column.")
            return None

    final_data = {}
    processed_rows = 0
    skipped_rows = 0

    # --- 增强的输入校验：检查核心列的空值情况 ---
    source_column_name = column_mappings.get("source_text")
    translation_column_name = column_mappings.get("translation_text")
    warning_messages = []

    if source_column_name and source_column_name in df.columns:
        source_null_ratio = df[source_column_name].isnull().mean()
        if source_null_ratio > 0.5: # 超过50%为空则警告
            warning_messages.append(f"警告：输入文件中映射为“原文”的列 ('{source_column_name}') 有 {source_null_ratio:.0%} 的数据为空。")
    else:
        # 这个情况应该在之前的核心列检查中被捕获，但作为双重检查
        if "source_text" in expected_columns_config_local and expected_columns_config_local["source_text"].get("is_core"):
             warning_messages.append("警告：未能定位到映射为“原文”的列，或该列不存在于Excel中。")

    if translation_column_name and translation_column_name in df.columns:
        translation_null_ratio = df[translation_column_name].isnull().mean()
        if translation_null_ratio > 0.5: # 超过50%为空则警告
            warning_messages.append(f"警告：输入文件中映射为“译文”的列 ('{translation_column_name}') 有 {translation_null_ratio:.0%} 的数据为空。")
    else:
        if "translation_text" in expected_columns_config_local and expected_columns_config_local["translation_text"].get("is_core"):
            warning_messages.append("警告：未能定位到映射为“译文”的列，或该列不存在于Excel中。")

    if warning_messages:
        for msg in warning_messages:
            st.warning(msg)
    # --- 结束 增强的输入校验 ---

    for _, row in df.iterrows():
        record_id_val = row.get(column_mappings.get("record_id"))
        record_id = record_id_val if pd.notnull(record_id_val) else None
        
        file_name_val = row.get(column_mappings.get("file_name"))
        file_name = file_name_val if pd.notnull(file_name_val) else None
        
        source_val = row.get(column_mappings.get("source_text"))
        source = source_val if pd.notnull(source_val) else None
        
        translation_val = row.get(column_mappings.get("translation_text"))
        translation = translation_val if pd.notnull(translation_val) else None

        if not all([record_id, file_name, source is not None, translation is not None]): # Allow empty strings but not None for source/translation
            skipped_rows +=1
            continue

        speaker, comments_text, path_val, scenario_val, char_intro_val, char_style_val, tone_val, note_val = (None,) * 8

        if column_mappings.get("speaker") and column_mappings["speaker"] in row and pd.notnull(row[column_mappings["speaker"]]):
            speaker = row[column_mappings["speaker"]]
        if column_mappings.get("comments") and column_mappings["comments"] in row and pd.notnull(row[column_mappings["comments"]]):
            comments_text = row[column_mappings["comments"]]
        if column_mappings.get("path") and column_mappings["path"] in row and pd.notnull(row[column_mappings["path"]]):
            path_val = row[column_mappings["path"]]
        if column_mappings.get("scenario") and column_mappings["scenario"] in row and pd.notnull(row[column_mappings["scenario"]]):
            scenario_val = row[column_mappings["scenario"]]
        if column_mappings.get("char_intro") and column_mappings["char_intro"] in row and pd.notnull(row[column_mappings["char_intro"]]):
            char_intro_val = row[column_mappings["char_intro"]]
        if column_mappings.get("char_style") and column_mappings["char_style"] in row and pd.notnull(row[column_mappings["char_style"]]):
            char_style_val = row[column_mappings["char_style"]]
        if column_mappings.get("tone") and column_mappings["tone"] in row and pd.notnull(row[column_mappings["tone"]]):
            tone_val = row[column_mappings["tone"]]
        if column_mappings.get("note") and column_mappings["note"] in row and pd.notnull(row[column_mappings["note"]]):
            note_val = row[column_mappings["note"]]

        entry = {
            "分类": file_name_mapping_dict.get(str(file_name), "未知分类"),
            "原文": str(source) if source is not None else "",
            "译文": str(translation) if translation is not None else ""
        }

        character_details = {}
        if speaker: character_details["说话人 Speaker"] = speaker
        if char_intro_val: character_details["角色介绍"] = char_intro_val
        if char_style_val: character_details["角色语言风格"] = char_style_val
        if character_details: entry["角色相关信息"] = character_details

        translation_background = {}
        if comments_text: translation_background["EN Source Comments"] = comments_text
        if tone_val: translation_background["语气"] = tone_val
        if note_val: translation_background["注意"] = note_val
        if path_val: translation_background["路径 Path"] = path_val
        if scenario_val: translation_background["Scenario"] = scenario_val
        if translation_background: entry["翻译背景信息"] = translation_background
            
        rhyme_keywords = ["押韵", "韵律", "韵脚", "韵"]
        if note_val and isinstance(note_val, str) and any(keyword in note_val for keyword in rhyme_keywords):
            entry["押韵"] = "1"
        else:
            entry["押韵"] = "0"
            
        final_data[str(record_id)] = entry
        processed_rows += 1

    if skipped_rows > 0:
        st.warning(f"{skipped_rows}行因核心字段缺失或无效被跳过。")
    if not final_data and processed_rows == 0:
        st.info("没有数据被处理。请检查Excel文件内容和列映射。")
        return None
        
    return final_data
