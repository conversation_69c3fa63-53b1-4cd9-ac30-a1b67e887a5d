import pandas as pd
import json
import re
import logging

from .config import (
    ALLOWED_IA_CONTENT_RT, ALLOWED_GP_CONTENT_RT, ALLOWED_ZHY_ID_RT,
    ALLOWED_SIMPLE_TAGS_RT, PLACEHOLDER_TAG_PATTERN_RT, GENERAL_TAG_FINDER_RT
)

logger = logging.getLogger(__name__)

def check_tag_compliance_rt(tag_match):
    full_tag_text = tag_match.group(0)
    tag_header = tag_match.group(1) # <...> part
    tag_name_part = tag_match.group(2) # tag name or zhy header
    content = tag_match.group(3) # content inside the tag

    zhy_pattern_str = r'<zhy\s+id=\"(ia|ib)\"\s+txt=\".*?\">'
    zhy_match = re.fullmatch(zhy_pattern_str, tag_name_part)

    if zhy_match:
        return True, None
    if tag_name_part == "IA":
        return (True, None) if content in ALLOWED_IA_CONTENT_RT else (False, f"标签 <IA> 包含无效内容: '{content[:30]}...'")
    if tag_name_part == "GP":
        return (True, None) if content in ALLOWED_GP_CONTENT_RT else (False, f"标签 <GP> 包含无效内容: '{content[:30]}...'")
    if tag_name_part in ALLOWED_SIMPLE_TAGS_RT:
        return True, None
    if PLACEHOLDER_TAG_PATTERN_RT.match(tag_name_part):
        return True, None
    return False, f"未知的标签类型或格式: '{tag_header[:40]}...'"

def validate_text_rt(text):
    failure_reasons = []
    if not isinstance(text, str):
        return True, False, ""

    potential_tags = list(GENERAL_TAG_FINDER_RT.finditer(text))
    if not potential_tags:
        if '<' in text or '>' in text:
             return False, False, "发现不成对的尖括号 '<' 或 '>'"
        return True, False, ""

    all_tags_compliant = True
    matched_spans = []
    for match in potential_tags:
        is_compliant, reason = check_tag_compliance_rt(match)
        if not is_compliant:
            all_tags_compliant = False
            if reason: failure_reasons.append(reason)
        matched_spans.append(match.span())

    matched_spans.sort(key=lambda x: x[0], reverse=True)
    temp_cleaned_text = list(text)
    for start, end in matched_spans:
        try:
            del temp_cleaned_text[start:end]
        except IndexError:
            logger.debug(f"IndexError during tag removal in validate_text_rt. Text: {text[:50]}..., Span: ({start},{end})")
            pass 
    final_cleaned_text = "".join(temp_cleaned_text)

    if '<' in final_cleaned_text or '>' in final_cleaned_text:
        if all_tags_compliant:
            all_tags_compliant = False
        failure_reasons.append("移除已识别标签后仍发现残留的 '<' 或 '>' (可能存在嵌套错误、未闭合标签或未知结构)")
    
    return all_tags_compliant, True, "; ".join(list(set(failure_reasons)))

def extract_tags_rt(text):
    if not isinstance(text, str): return []
    return [match.group(1) for match in GENERAL_TAG_FINDER_RT.finditer(text)]

def run_rich_text_check_core(json_data_str: str, original_text_key: str, translated_text_key: str):
    results = []
    processed_count, skipped_count, compliant_count, non_compliant_count, failed_count = 0,0,0,0,0
    data = None
    
    try: 
        data = json.loads(json_data_str)
        logger.info(f"JSON数据成功解析，包含 {len(data)} 条记录")
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}. 输入数据 (前500字符): {json_data_str[:500] + '...' if len(json_data_str) > 500 else json_data_str}")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}

    if not data:
        logger.error("JSON数据为空。")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}
        
    iterator, total_items, get_item, get_id = None, 0, None, None
    if isinstance(data, list):
        iterator, total_items = enumerate(data), len(data)
        get_item, get_id = lambda item_enum: item_enum[1], lambda item_enum: f"List Index {item_enum[0]}"
    elif isinstance(data, dict):
        iterator, total_items = data.items(), len(data)
        get_item, get_id = lambda item_kv: item_kv[1], lambda item_kv: item_kv[0]
    else: 
        logger.error(f"错误: 不支持的 JSON 顶层结构。类型: {type(data)}")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}
    
    samples_success = 0
    sample_items_info = []
    
    check_iterator = data.items() if isinstance(data, dict) else enumerate(data)
    for i, item_data_iter_check in enumerate(check_iterator):
        if i >= 5:
            break
        try:
            entry_id_check = get_id(item_data_iter_check)
            entry_data_check = get_item(item_data_iter_check)
            sample_items_info.append(f"ID: {entry_id_check}, Keys: {list(entry_data_check.keys()) if isinstance(entry_data_check, dict) else type(entry_data_check)}")
            if isinstance(entry_data_check, dict) and original_text_key in entry_data_check and translated_text_key in entry_data_check:
                samples_success += 1
        except Exception as e_check:
            logger.warning(f"检查样本记录 (ID/Index {i}) 时出错: {e_check}")
    
    if samples_success == 0 and total_items > 0 :
        logger.error(f"在JSON数据前5条记录中无法找到指定的键: '{original_text_key}' 或 '{translated_text_key}'")
        if sample_items_info:
            logger.info("记录样本详情:")
            for sample_info in sample_items_info:
                logger.info(f"- {sample_info}")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}

    iterator = data.items() if isinstance(data, dict) else enumerate(data)

    logger.info("开始富文本检查...")
    for i, item_data_iter in enumerate(iterator):
        processed_count += 1
        details, identifier, original_text, translated_text, status = "", "未知ID", "", "", "判定失败"
        try:
            identifier = get_id(item_data_iter); entry_data = get_item(item_data_iter)
            if not isinstance(entry_data, dict): raise TypeError(f"条目数据类型不是字典 ({type(entry_data)})")
            
            original_text = entry_data.get(original_text_key, "")
            translated_text = entry_data.get(translated_text_key, "")

            if not isinstance(original_text, str):
                logger.warning(f"记录 '{identifier}' 的原文不是字符串 (类型: {type(original_text)}), 将视为空字符串。值: {str(original_text)[:50]}")
                original_text = str(original_text) if original_text is not None else ""
            if not isinstance(translated_text, str):
                logger.warning(f"记录 '{identifier}' 的译文不是字符串 (类型: {type(translated_text)}), 将视为空字符串。值: {str(translated_text)[:50]}")
                translated_text = str(translated_text) if translated_text is not None else ""

            compliant_orig, found_tags_orig, reason_orig = validate_text_rt(original_text)
            compliant_trans, found_tags_trans, reason_trans = validate_text_rt(translated_text)
            
            tags_orig, tags_trans = extract_tags_rt(original_text), extract_tags_rt(translated_text)
            tags_match = (tags_orig == tags_trans)
            
            should_skip = (compliant_orig and not found_tags_orig) and (compliant_trans and not found_tags_trans)
            
            if should_skip:
                skipped_count += 1
                if (i + 1) % 100 == 0 or (i + 1) == total_items :
                     logger.info(f"富文本检查中... {i+1}/{total_items} (已跳过 {skipped_count})")
                continue

            final_compliant = compliant_orig and compliant_trans and tags_match
            reason_details = []
            if not compliant_orig and reason_orig: reason_details.append(f"{original_text_key}: {reason_orig}")
            if not compliant_trans and reason_trans: reason_details.append(f"{translated_text_key}: {reason_trans}")
            if not tags_match: reason_details.append("原文和译文的富文本标签不匹配")
            details = "; ".join(reason_details)
            
            if not final_compliant: 
                status = "不合规"; non_compliant_count += 1
            else: 
                status = "合规"; compliant_count += 1; details = ""
        
        except (AttributeError, TypeError, KeyError) as e_inner: 
            status, details, failed_count = "判定失败", f"结构错误: {e_inner}", failed_count+1
            logger.debug(f"记录 {identifier} 结构错误: {e_inner}", exc_info=True)
        except Exception as e_inner: 
            status, details, failed_count = "判定失败", f"检查文本意外错误: {e_inner}", failed_count+1
            logger.warning(f"记录 {identifier} 检查时意外错误: {e_inner}", exc_info=True)
            
        results.append({"Index": identifier, "Result": status, "Details": details})
        
        if (i + 1) % 100 == 0 or (i + 1) == total_items :
            logger.info(f"富文本检查中... {i+1}/{total_items} (跳过: {skipped_count}, 不合规: {non_compliant_count}, 失败: {failed_count})")

    logger.info("富文本检查完成！")
    
    empty_df = pd.DataFrame(columns=["Index", "Result", "Details"])
    
    stats = {
        "processed": processed_count,
        "skipped": skipped_count,
        "compliant": compliant_count,
        "non_compliant": non_compliant_count, 
        "failed": failed_count
    }
    
    if results: 
        return pd.DataFrame(results, columns=["Index", "Result", "Details"]), stats
    else: 
        logger.info("没有条目需要进行富文本检查或所有条目均被跳过。")
        return empty_df, stats
