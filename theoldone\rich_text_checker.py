import streamlit as st
import pandas as pd
import json
import re
from config import (
    ALLOWED_IA_CONTENT_RT, ALLOWED_GP_CONTENT_RT, ALLOWED_ZHY_ID_RT,
    ALLOWED_SIMPLE_TAGS_RT, PLACEHOLDER_TAG_PATTERN_RT, GENERAL_TAG_FINDER_RT
)

def check_tag_compliance_rt(tag_match):
    full_tag_text = tag_match.group(0)
    tag_header = tag_match.group(1) # <...> part
    tag_name_part = tag_match.group(2) # tag name or zhy header
    content = tag_match.group(3) # content inside the tag

    zhy_match = re.match(r'<zhy\s+id=\"(?P<id>ia|ib)\"\\s+txt=\".*?\"', tag_name_part)
    if zhy_match:
        return True, None
    if tag_name_part == "IA":
        return (True, None) if content in ALLOWED_IA_CONTENT_RT else (False, f"标签 <IA> 包含无效内容: '{content[:30]}...'")
    if tag_name_part == "GP":
        return (True, None) if content in ALLOWED_GP_CONTENT_RT else (False, f"标签 <GP> 包含无效内容: '{content[:30]}...'")
    if tag_name_part in ALLOWED_SIMPLE_TAGS_RT:
        return True, None
    if PLACEHOLDER_TAG_PATTERN_RT.match(tag_name_part):
        return True, None
    return False, f"未知的标签类型或格式: '{tag_header[:40]}...'"

def validate_text_rt(text):
    failure_reasons = []
    if not isinstance(text, str):
        return True, False, ""

    potential_tags = list(GENERAL_TAG_FINDER_RT.finditer(text))
    if not potential_tags:
        if '<' in text or '>' in text:
             return False, False, "发现不成对的尖括号 '<' 或 '>'"
        return True, False, ""

    all_tags_compliant = True
    matched_spans = []
    for match in potential_tags:
        is_compliant, reason = check_tag_compliance_rt(match)
        if not is_compliant:
            all_tags_compliant = False
            if reason: failure_reasons.append(reason)
        matched_spans.append(match.span())

    matched_spans.sort(key=lambda x: x[0], reverse=True)
    temp_cleaned_text = list(text)
    for start, end in matched_spans:
        try: del temp_cleaned_text[start:end]
        except IndexError: pass
    final_cleaned_text = "".join(temp_cleaned_text)

    if '<' in final_cleaned_text or '>' in final_cleaned_text:
        if all_tags_compliant: all_tags_compliant = False
        failure_reasons.append("移除已识别标签后仍发现残留的 '<' 或 '>' (可能存在嵌套错误、未闭合标签或未知结构)")
    
    return all_tags_compliant, True, "; ".join(list(set(failure_reasons)))

def extract_tags_rt(text):
    if not isinstance(text, str): return []
    return [match.group(1) for match in GENERAL_TAG_FINDER_RT.finditer(text)]

def run_rich_text_check_core(json_data_str, original_text_key, translated_text_key):
    results = []
    processed_count, skipped_count, compliant_count, non_compliant_count, failed_count = 0,0,0,0,0
    data = None
    try: 
        data = json.loads(json_data_str)
        st.success(f"JSON数据成功解析，包含 {len(data)} 条记录")
    except json.JSONDecodeError as e:
        st.error(f"JSON解析错误: {e}.")
        st.code(json_data_str[:500] + "..." if len(json_data_str) > 500 else json_data_str)
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}

    if not data:
        st.error("JSON数据为空。")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}
        
    iterator, total_items, get_item, get_id = None, 0, None, None
    if isinstance(data, list):
        iterator, total_items = enumerate(data), len(data)
        get_item, get_id = lambda item_enum: item_enum[1], lambda item_enum: f"List Index {item_enum[0]}"
    elif isinstance(data, dict):
        iterator, total_items = data.items(), len(data)
        get_item, get_id = lambda item_kv: item_kv[1], lambda item_kv: item_kv[0]
    else: 
        st.error(f"错误: 不支持的 JSON 顶层结构。类型: {type(data)}")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}
    
    samples_success = 0
    sample_items = []
    
    check_iterator = data.items() if isinstance(data, dict) else enumerate(data)
    for i, item_data_iter in enumerate(check_iterator):
        if i >= 5:
            break
        try:
            entry_id = get_id(item_data_iter)
            entry_data = get_item(item_data_iter)
            sample_items.append((entry_id, entry_data))
            if isinstance(entry_data, dict) and original_text_key in entry_data and translated_text_key in entry_data:
                samples_success += 1
        except Exception as e:
            st.warning(f"检查第 {i} 项时出错: {e}")
    
    if samples_success == 0:
        st.error(f"在JSON数据前5条记录中无法找到指定的键: '{original_text_key}' 或 '{translated_text_key}'")
        if sample_items:
            st.write("记录示例:")
            for idx, (entry_id, entry_data) in enumerate(sample_items):
                st.write(f"项目 {idx+1} (ID: {entry_id}):")
                if isinstance(entry_data, dict):
                    st.write("包含的键:", list(entry_data.keys()))
                else:
                    st.write(f"数据类型: {type(entry_data)}")
        return pd.DataFrame(), {"processed": 0, "skipped": 0, "compliant": 0, "non_compliant": 0, "failed": 0}

    iterator = data.items() if isinstance(data, dict) else enumerate(data)

    progress_bar_rt = st.progress(0); status_text_rt = st.empty(); status_text_rt.text("开始富文本检查...")
    for i, item_data_iter in enumerate(iterator):
        processed_count += 1
        details, identifier, original_text, translated_text, status = "", "未知ID", "", "", "判定失败"
        try:
            identifier = get_id(item_data_iter); entry_data = get_item(item_data_iter)
            if not isinstance(entry_data, dict): raise TypeError(f"条目数据类型不是字典 ({type(entry_data)})")
            original_text = entry_data.get(original_text_key, "")
            translated_text = entry_data.get(translated_text_key, "")
            compliant_orig, found_tags_orig, reason_orig = validate_text_rt(original_text)
            compliant_trans, found_tags_trans, reason_trans = validate_text_rt(translated_text)
            tags_orig, tags_trans = extract_tags_rt(original_text), extract_tags_rt(translated_text)
            tags_match = (tags_orig == tags_trans)
            should_skip = (compliant_orig and not found_tags_orig) and (compliant_trans and not found_tags_trans)
            if should_skip:
                skipped_count += 1; status_text_rt.text(f"富文本检查中... {i+1}/{total_items} (跳过)")
                progress_bar_rt.progress((i + 1) / total_items); continue
            final_compliant = compliant_orig and compliant_trans and tags_match
            reason_details = []
            if not compliant_orig and reason_orig: reason_details.append(f"{original_text_key}: {reason_orig}")
            if not compliant_trans and reason_trans: reason_details.append(f"{translated_text_key}: {reason_trans}")
            if not tags_match: reason_details.append("原文和译文的富文本标签不匹配")
            details = "; ".join(reason_details)
            if not final_compliant: status = "不合规"; non_compliant_count += 1
            else: status = "合规"; compliant_count += 1; details = ""
        except (AttributeError, TypeError, KeyError) as e_inner: status, details, failed_count = "判定失败", f"结构错误: {e_inner}", failed_count+1
        except Exception as e_inner: status, details, failed_count = "判定失败", f"检查文本意外错误: {e_inner}", failed_count+1
        results.append({"Index": identifier, "Result": status, "Details": details})
        status_text_rt.text(f"富文本检查中... {i+1}/{total_items}"); progress_bar_rt.progress((i + 1) / total_items)
    status_text_rt.text("富文本检查完成！"); progress_bar_rt.empty()
    
    empty_df = pd.DataFrame(columns=["Index", "Result", "Details"])
    
    stats = {
        "processed": processed_count,
        "skipped": skipped_count,
        "compliant": compliant_count,
        "non_compliant": non_compliant_count, 
        "failed": failed_count
    }
    
    if results: 
        return pd.DataFrame(results, columns=["Index", "Result", "Details"]), stats
    else: 
        st.info("没有发现包含富文本的条目。")
        empty_df.attrs["valid_result"] = True
        return empty_df, stats
