# app/main_cli.py
import argparse
import logging
import os
import sys
import json
import pandas as pd # 新增，用于创建DataFrame
import threading # 新增，用于停止事件 (可选)

# 真实导入
from . import config
from . import utils
from . import preprocess
from . import rich_text_checker
from . import translation_validator # 新增
from . import translation_scorer # 新增 scorer 模块导入

# 配置基本的日志记录
logging.basicConfig(stream=sys.stdout, level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 移除了 run_rich_text_check_placeholder

def prepare_validation_tasks(processed_data: dict) -> list:
    """根据预处理后的数据准备AI翻译校验任务列表。"""
    tasks = []
    if not isinstance(processed_data, dict):
        logger.error("预处理数据格式不正确，应为字典。")
        return tasks

    for key, item_details in processed_data.items():
        if not isinstance(item_details, dict):
            logger.warning(f"条目 {key} 的数据格式不正确，跳过。")
            continue

        category = item_details.get("分类", "未知分类")
        original_text = item_details.get(config.DEFAULT_PREPROCESS_ORIGINAL_TEXT_KEY, "")
        translated_text = item_details.get(config.DEFAULT_PREPROCESS_TRANSLATED_TEXT_KEY, "")
        
        # 从 item_details 中提取角色和背景信息，如果不存在则为空字典或字符串
        character_info_dict = item_details.get("角色相关信息", {})
        translation_background_dict = item_details.get("翻译背景信息", {})

        # 将字典转换为字符串以便填充到提示中
        # 例如: "说话人 Speaker: xxx; 角色介绍: yyy"
        character_info_str = "; ".join([f"{k}: {v}" for k, v in character_info_dict.items() if v]) if character_info_dict else "无"
        translation_background_str = "; ".join([f"{k}: {v}" for k, v in translation_background_dict.items() if v]) if translation_background_dict else "无"
        is_rhyming_entry = item_details.get("押韵", "0") == "1"

        if not original_text or not translated_text:
            logger.warning(f"条目 {key} 的原文或译文为空，跳过AI校验任务生成。")
            continue

        # 准备标准分析任务
        standard_prompt = config.STANDARD_PROMPT_TEXT_VALIDATOR.format(
            category=category,
            original_text=original_text,
            translated_text=translated_text,
            character_info=character_info_str,
            translation_background=translation_background_str
        )
        tasks.append({
            "key": key,
            "item": item_details, # 传递整个item，analyze_translation_validator会提取原文/译文
            "prompt": standard_prompt,
            "type": "standard"
        })

        # 如果需要押韵，准备韵律分析任务
        if is_rhyming_entry:
            rhyming_prompt = config.RHYMING_PROMPT_TEXT_VALIDATOR.format(
                category=category,
                original_text=original_text,
                translated_text=translated_text,
                character_info=character_info_str,
                translation_background=translation_background_str
            )
            tasks.append({
                "key": key, # 使用相同的key，结果会在validator内部聚合
                "item": item_details,
                "prompt": rhyming_prompt,
                "type": "rhyme"
            })
    logger.info(f"为AI翻译校验准备了 {len(tasks)} 个任务。")
    return tasks

def main():
    parser = argparse.ArgumentParser(description="翻译自动化辅助处理流程 CLI")
    # --- 通用参数 ---
    parser.add_argument("--input_excel", required=True, help="原始Excel输入文件路径")
    parser.add_argument("--output_dir", required=True, help="所有输出文件的目标目录")
    
    # --- 预处理 & 富文本检查参数 ---
    parser.add_argument("--original_text_key", default=config.DEFAULT_PREPROCESS_ORIGINAL_TEXT_KEY, help="预处理后JSON中原文的键名")
    parser.add_argument("--translated_text_key", default=config.DEFAULT_PREPROCESS_TRANSLATED_TEXT_KEY, help="预处理后JSON中译文的键名")

    # --- AI翻译校验参数 ---
    parser.add_argument("--qwen_api_key", help="Qwen API Key (用于标准校验、直译、拆解)")
    parser.add_argument("--deepseek_api_key", help="DeepSeek API Key (用于韵律校验)")
    
    parser.add_argument("--qwen_validator_model", default=config.QWEN3_MODEL_VALIDATOR, help="Qwen模型用于标准翻译校验")
    parser.add_argument("--qwen_validator_base_url", default=config.QWEN3_BASE_URL_VALIDATOR, help="Qwen校验模型API基础URL")
    parser.add_argument("--deepseek_validator_model", default=config.DEEPSEEK_MODEL_VALIDATOR, help="DeepSeek模型用于韵律翻译校验")
    parser.add_argument("--deepseek_validator_base_url", default=config.DEEPSEEK_BASE_URL_VALIDATOR, help="DeepSeek校验模型API基础URL")
    
    parser.add_argument("--enable_literal_translation", action='store_true', help="启用中英直译功能")
    parser.add_argument("--literal_translator_model", default=config.LITERAL_TRANSLATOR_MODEL, help="中英直译模型")
    parser.add_argument("--literal_translator_base_url", default=config.LITERAL_TRANSLATOR_BASE_URL, help="中英直译API基础URL")
    
    parser.add_argument("--enable_decomposition", action='store_true', help="启用翻译拆解功能")
    parser.add_argument("--decomposition_model", default=config.DECOMPOSITION_MODEL, help="翻译拆解模型")
    parser.add_argument("--decomposition_base_url", default=config.DECOMPOSITION_BASE_URL, help="翻译拆解API基础URL")

    parser.add_argument("--validator_max_workers", type=int, default=5, help="AI翻译校验的并发线程数")
    parser.add_argument("--skip_scoring", action='store_true', help="跳过AI翻译评分步骤") # 新增跳过评分参数

    args = parser.parse_args()

    # 日志记录所有参数
    logger.info("CLI 程序启动，参数:")
    for arg, value in vars(args).items():
        # 敏感信息如API Key部分打码或不记录完整值
        if "api_key" in arg and value is not None:
            logger.info(f"  {arg}: {'*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '****'}")
        else:
            logger.info(f"  {arg}: {value}")

    if not os.path.exists(args.output_dir):
        try:
            os.makedirs(args.output_dir)
            logger.info(f"创建输出目录: {args.output_dir}")
        except OSError as e:
            logger.error(f"无法创建输出目录 {args.output_dir}: {e}. 请确保挂载的卷具有写权限。")
            sys.exit(1)

    # 初始化停止事件 (暂未实现Ctrl+C处理，但validator内部会检查)
    # stop_event = threading.Event()

    processed_json_path = None
    validation_output_excel_path = None # 新增，用于后续评分步骤读取

    try:
        # --- 步骤1: 数据预处理 ---
        logger.info(f"{config.STEP_PREPROCESS}") # 使用config中的步骤名
        column_mappings = config.DEFAULT_COLUMN_MAPPINGS_PREPROCESS
        file_name_mapping = config.FILE_NAME_MAPPING_PREPROCESS
        expected_cols_config = config.EXPECTED_COLUMNS_CONFIG_PREPROCESS
        processed_data_dict = preprocess.preprocess_data_core_adapted(
            args.input_excel, column_mappings, file_name_mapping, expected_cols_config
        )
        if processed_data_dict:
            json_output_str = json.dumps(processed_data_dict, ensure_ascii=False, indent=2)
            processed_json_path = utils.save_json_string_to_file(
                json_output_str, args.output_dir, config.DEFAULT_PREPROCESS_OUTPUT_JSON_NAME
            )
            if processed_json_path: logger.info(f"数据预处理成功，输出: {processed_json_path}")
            else: logger.error("预处理后保存JSON失败。"); sys.exit(1)
        else: logger.error("数据预处理失败。"); sys.exit(1)

        # --- 步骤2: 富文本检查 ---
        if processed_json_path: 
            logger.info(f"{config.STEP_RICH_TEXT_CHECK}") # 使用config中的步骤名
            try:
                with open(processed_json_path, 'r', encoding='utf-8') as f_json:
                    input_json_for_rt_check = f_json.read()
                rt_results_df, rt_stats = rich_text_checker.run_rich_text_check_core(
                    input_json_for_rt_check, args.original_text_key, args.translated_text_key
                )
                logger.info(f"富文本检查统计: {rt_stats}")
                if rt_results_df is not None and not rt_results_df.empty:
                    # rich_text_output_excel_path = ... (save logic as before)
                    utils.save_dataframe_to_excel(rt_results_df, args.output_dir, config.DEFAULT_RICH_TEXT_OUTPUT_EXCEL_NAME)
                    logger.info(f"富文本检查报告已保存至: {os.path.join(args.output_dir, config.DEFAULT_RICH_TEXT_OUTPUT_EXCEL_NAME)}")
                else: logger.info("富文本检查未生成报告 (无不合规项或已跳过)。")
            except Exception as e_rt_main: logger.error(f"富文本检查步骤出错: {e_rt_main}", exc_info=True)

        # --- 步骤3: AI翻译校验 ---
        if processed_json_path:
            logger.info(f"{config.STEP_TRANSLATION_VALIDATION}") # 使用config中的步骤名
            if not args.qwen_api_key or not args.deepseek_api_key:
                logger.error("Qwen 或 DeepSeek API Key 未提供，无法进行AI翻译校验。请使用 --qwen_api_key 和 --deepseek_api_key 参数。")
            else:
                try:
                    # 预处理输出的JSON是processed_data_dict，不是路径
                    # with open(processed_json_path, 'r', encoding='utf-8') as f_val_json:
                    #     input_data_for_validation = json.load(f_val_json) # run_validation_batch需要原始字典数据
                    
                    validation_tasks = prepare_validation_tasks(processed_data_dict) # 使用预处理后的字典数据
                    
                    if validation_tasks:
                        validation_excel_rows = translation_validator.run_validation_batch(
                            tasks_to_run=validation_tasks,
                            qwen_model_name=args.qwen_validator_model,
                            deepseek_model_name=args.deepseek_validator_model,
                            qwen_api_key=args.qwen_api_key,
                            deepseek_api_key=args.deepseek_api_key,
                            qwen_base_url=args.qwen_validator_base_url,
                            deepseek_base_url=args.deepseek_validator_base_url,
                            enable_literal_translation=args.enable_literal_translation,
                            literal_translator_model_name=args.literal_translator_model,
                            literal_translator_base_url=args.literal_translator_base_url,
                            enable_decomposition=args.enable_decomposition,
                            decomposition_model_name=args.decomposition_model,
                            decomposition_base_url=args.decomposition_base_url,
                            max_workers=args.validator_max_workers
                            # validator_should_stop_event=stop_event # 传递停止事件
                        )

                        if validation_excel_rows:
                            validation_df = pd.DataFrame(validation_excel_rows)
                            # Sort by SortLevel (errors/unqualified first), then by Key
                            if 'SortLevel' in validation_df.columns and 'Key' in validation_df.columns:
                                validation_df.sort_values(by=["SortLevel", "Key"], ascending=[True, True], inplace=True)
                            
                            validation_output_excel_path = utils.save_dataframe_to_excel(
                                validation_df, 
                                args.output_dir, 
                                config.DEFAULT_VALIDATION_OUTPUT_EXCEL_NAME
                            )
                            if validation_output_excel_path:
                                logger.info(f"AI翻译校验报告成功保存到: {validation_output_excel_path}")
                            else:
                                logger.error("AI翻译校验报告保存失败。")
                        else:
                            logger.info("AI翻译校验未生成任何结果行。")
                    else:
                        logger.info("没有为AI翻译校验准备任何任务。")
                except Exception as e_val_main:
                    logger.error(f"AI翻译校验步骤发生主错误: {e_val_main}", exc_info=True)
        
        # --- 步骤4: AI翻译评分 ---
        if not args.skip_scoring and validation_output_excel_path and os.path.exists(validation_output_excel_path):
            logger.info(f"{config.STEP_TRANSLATION_SCORING}") # 使用config中的步骤名
            try:
                df_for_scoring = pd.read_excel(validation_output_excel_path, engine='openpyxl')
                # 假设 validator 输出的分析文本在 'Analysis' 列
                # translation_scorer.run_translation_scoring_core 默认期望 'Analysis' 列
                scored_df, scoring_stats = translation_scorer.run_translation_scoring_core(df_for_scoring)
                
                if scored_df is not None and not scored_df.empty:
                    scoring_output_path = utils.save_dataframe_to_excel(
                        scored_df,
                        args.output_dir,
                        config.DEFAULT_SCORING_OUTPUT_EXCEL_NAME
                    )
                    if scoring_output_path:
                        logger.info(f"AI翻译评分报告已保存至: {scoring_output_path}")
                        logger.info(f"评分统计: {scoring_stats}")
                    else:
                        logger.error("AI翻译评分报告保存失败。")
                else:
                    logger.info("AI翻译评分未生成有效结果或DataFrame为空。")
            except FileNotFoundError:
                logger.error(f"AI翻译评分错误：找不到AI翻译校验的输出文件 {validation_output_excel_path}")
            except Exception as e_score_main:
                logger.error(f"AI翻译评分步骤发生错误: {e_score_main}", exc_info=True)
        elif args.skip_scoring:
            logger.info(f"已跳过 {config.STEP_TRANSLATION_SCORING} 步骤。")
        elif not (validation_output_excel_path and os.path.exists(validation_output_excel_path)):
             logger.info(f"跳过 {config.STEP_TRANSLATION_SCORING} 步骤，因为未找到AI翻译校验的输出文件或校验未成功执行。")

        logger.info("--- 所有 CLI 步骤处理完成 ---")

    except Exception as e:
        logger.error(f"处理流程中发生严重错误: {e}", exc_info=True)
        sys.exit(1)
    # finally:
        # if stop_event.is_set():
            # logger.info("CLI处理因用户中断而提前结束。")

if __name__ == "__main__":
    main() 