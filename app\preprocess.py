import pandas as pd
import json
import logging

logger = logging.getLogger(__name__)

def preprocess_data_core_adapted(
    uploaded_file_path: str,
    column_mappings: dict,
    file_name_mapping_dict: dict,
    expected_columns_config: dict
):
    try:
        df = pd.read_excel(uploaded_file_path, sheet_name='Sheet0') # 假设总是读取第一个 sheet
    except FileNotFoundError:
        logger.error(f"预处理错误: 输入文件未找到路径 '{uploaded_file_path}'")
        return None
    except Exception as e:
        logger.error(f"预处理错误: 读取Excel文件 '{uploaded_file_path}' 失败: {e}", exc_info=True)
        return None

    # 1. 验证核心列映射是否有效且存在于Excel中
    for logical_key, config in expected_columns_config.items():
        description = config.get("description", logical_key)
        if config.get("is_core", False):
            if logical_key not in column_mappings:
                logger.error(
                    f"预处理配置错误: 核心字段 '{description}' (逻辑键: '{logical_key}') "
                    f"未在列映射中提供。该字段对于处理至关重要。"
                )
                return None
            
            excel_column_name = column_mappings.get(logical_key)
            if not excel_column_name: # 映射到了一个空或None的列名
                logger.error(
                    f"预处理配置错误: 核心字段 '{description}' (逻辑键: '{logical_key}') "
                    f"被映射到了一个空的Excel列名。请为此核心字段选择一个有效的Excel列。"
                )
                return None
            
            if excel_column_name not in df.columns:
                logger.error(
                    f"预处理错误: 核心字段 '{description}' (逻辑键: '{logical_key}') "
                    f"被映射到Excel列 '{excel_column_name}'，但该列在上传的文件中未找到。"
                    f"可用列: {df.columns.tolist()}"
                )
                return None

    final_data = {}
    processed_rows_count = 0
    skipped_rows_count = 0

    # 2. （可选）检查核心列的空值比例 (从原型中借鉴)
    source_column_excel_name = column_mappings.get("source_text")
    translation_column_excel_name = column_mappings.get("translation_text")

    if source_column_excel_name and source_column_excel_name in df.columns:
        source_null_ratio = df[source_column_excel_name].isnull().mean()
        if source_null_ratio > 0.5:
            logger.warning(
                f"预处理警告: 文件 '{uploaded_file_path}' 中, 列 '{source_column_excel_name}' "
                f"(映射到 'source_text') 有 {source_null_ratio:.0%} 的空值。"
            )

    if translation_column_excel_name and translation_column_excel_name in df.columns:
        translation_null_ratio = df[translation_column_excel_name].isnull().mean()
        if translation_null_ratio > 0.5:
            logger.warning(
                f"预处理警告: 文件 '{uploaded_file_path}' 中, 列 '{translation_column_excel_name}' "
                f"(映射到 'translation_text') 有 {translation_null_ratio:.0%} 的空值。"
            )

    # 3. 遍历行并处理数据
    for index, row in df.iterrows():
        # 辅助函数，用于从行中安全地获取映射值
        def get_mapped_value(logical_key_local):
            excel_col_name = column_mappings.get(logical_key_local)
            if excel_col_name and excel_col_name in row:
                val = row[excel_col_name]
                return val if pd.notnull(val) else None # 如果是 NaN/NaT/None 则返回 Python None
            return None

        record_id = get_mapped_value("record_id")
        file_name = get_mapped_value("file_name")
        source_text = get_mapped_value("source_text")     # 如果单元格为空，这里是 None
        translation_text = get_mapped_value("translation_text") # 如果单元格为空，这里是 None

        # 核心字段校验逻辑 (参考原型)
        # record_id 和 file_name 必须存在且在转为字符串后非空
        # source_text 和 translation_text 必须非 None (空字符串是允许的)
        
        valid_record_id = record_id is not None and str(record_id).strip()
        valid_file_name = file_name is not None and str(file_name).strip()
        valid_source = source_text is not None
        valid_translation = translation_text is not None

        if not (valid_record_id and valid_file_name and valid_source and valid_translation):
            skipped_rows_count += 1
            debug_reasons = []
            if not valid_record_id: debug_reasons.append(f"'record_id'缺失/为空 (值: {record_id})")
            if not valid_file_name: debug_reasons.append(f"'file_name'缺失/为空 (值: {file_name})")
            if not valid_source: debug_reasons.append(f"'source_text'为None (Excel单元格可能为空)")
            if not valid_translation: debug_reasons.append(f"'translation_text'为None (Excel单元格可能为空)")
            logger.debug(f"预处理: 第 {index + 2} 行被跳过. 原因: {'; '.join(debug_reasons)}.")
            continue
        
        # 确保核心ID和名称是字符串形式
        record_id_str = str(record_id).strip()
        file_name_str = str(file_name).strip()

        # 获取可选字段
        speaker = get_mapped_value("speaker")
        comments_text = get_mapped_value("comments")
        path_val = get_mapped_value("path")
        scenario_val = get_mapped_value("scenario")
        char_intro_val = get_mapped_value("char_intro")
        char_style_val = get_mapped_value("char_style")
        tone_val = get_mapped_value("tone")
        note_val = get_mapped_value("note")
        
        entry = {
            "分类": file_name_mapping_dict.get(file_name_str, "未知分类"),
            "原文": str(source_text) if source_text is not None else "",
            "译文": str(translation_text) if translation_text is not None else ""
        }

        character_details = {}
        if speaker: character_details["说话人 Speaker"] = str(speaker)
        if char_intro_val: character_details["角色介绍"] = str(char_intro_val)
        if char_style_val: character_details["角色语言风格"] = str(char_style_val)
        if character_details: entry["角色相关信息"] = character_details

        translation_background = {}
        if comments_text: translation_background["EN Source Comments"] = str(comments_text)
        if tone_val: translation_background["语气"] = str(tone_val)
        if note_val: translation_background["注意"] = str(note_val)
        if path_val: translation_background["路径 Path"] = str(path_val)
        if scenario_val: translation_background["Scenario"] = str(scenario_val)
        if translation_background: entry["翻译背景信息"] = translation_background
            
        rhyme_keywords = ["押韵", "韵律", "韵脚", "韵"] # 未来可以考虑配置化
        if note_val and isinstance(note_val, str) and any(keyword in note_val for keyword in rhyme_keywords):
            entry["押韵"] = "1"
        else:
            entry["押韵"] = "0"
            
        final_data[record_id_str] = entry
        processed_rows_count += 1

    if skipped_rows_count > 0:
        logger.warning(
            f"预处理文件 '{uploaded_file_path}' 完成: {skipped_rows_count} 行因核心字段缺失或无效被跳过。"
            f"详情请查看DEBUG级别日志。"
        )
    
    if not final_data and processed_rows_count == 0:
        logger.warning(
            f"预处理警告: 文件 '{uploaded_file_path}' 未处理任何数据行。"
            f"这可能是因为所有行都被跳过，或数据部分为空。总行数 (不含表头): {len(df)}。"
            f"已跳过行数: {skipped_rows_count}。"
        )
        return None # 表示没有数据可处理，与原型行为一致
        
    logger.info(
        f"预处理成功: 从文件 '{uploaded_file_path}' 处理了 {processed_rows_count} 行。"
        f"跳过了 {skipped_rows_count} 行。"
    )
    return final_data
