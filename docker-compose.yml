version: '3.8'

services:
  cli-app: # Our existing CLI application
    build:
      context: .
      dockerfile: Dockerfile # Points to the Dockerfile for the CLI app
    image: auto-check-cli-via-compose # Give it a distinct name if built via compose
    entrypoint: tail # Override the Dockerfile's ENTRYPOINT
    command: ["-f", "/dev/null"] # Arguments for the new entrypoint
    # This service doesn't run on its own, it's invoked by the web-server.
    # It needs access to the same volumes if web-server writes inputs to shared locations.
    # However, our current web-server calls 'docker run' on the HOST's docker, 
    # so this service definition is more for reference or if we change invocation strategy.

  web-server:
    build:
      context: . # Build context is the project root
      dockerfile: web_server/Dockerfile.web # Points to the new Dockerfile for the web server
    image: auto-check-web-server
    ports:
      - "8000:8000" # Map port 8000 on the host to port 8000 in the container
    volumes:
      # Mount the Docker socket to allow this container to run Docker commands (docker run auto-check-cli)
      - /var/run/docker.sock:/var/run/docker.sock
      # Mount directories for uploads, outputs, and logs from the host to the web-server container
      # These paths are relative to the docker-compose.yml file location (project root)
      - ./web_server/uploads:/usr/src/web_app/uploads 
      - ./web_server/outputs:/usr/src/web_app/outputs
      - ./web_server/logs:/usr/src/web_app/logs
    environment:
      # Pass API keys as environment variables to the web-server container
      # These will be picked up by os.getenv in main_web.py
      # You SHOULD use a .env file for these in a real scenario, not hardcode them here.
      - QWEN_API_KEY=${QWEN_API_KEY_HOST}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY_HOST}
      - HOST_PROJECT_DIR=${PWD}
      # Potentially other configurations like base URLs if they need to be dynamic
    depends_on:
      - cli-app # Optional: Ensures cli-app image is built first, though not strictly necessary for current invocation

# Optional: Define top-level volumes if you want Docker to manage their lifecycle,
# but for simple host-path mapping, the above is fine.
# volumes:
#   uploads_data:
#   outputs_data:
#   logs_data: 