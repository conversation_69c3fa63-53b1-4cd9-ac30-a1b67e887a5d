import streamlit as st
import time
from openai import OpenAI, APIStatusError, APITimeoutError
from config import DECOMPOSITION_PROMPT, DECOMPOSITION_MAX_RETRIES, OPENAI_REQUEST_TIMEOUT_SECONDS

def explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count=0):
    try:
        should_stop_early = False
        try: # Check session_state safely for early stop
            should_stop_early = st.session_state.get('decomposition_should_stop', False) or \
                          st.session_state.get('validator_should_stop', False)
        except AttributeError: pass
        if should_stop_early: return "处理被用户停止 (翻译拆解模块)"

        response = api_client.chat.completions.create(
            model=model_name, 
            messages=[{"role": "system", "content": DECOMPOSITION_PROMPT}, {"role": "user", "content": f"请解释这段文本: {translation_text}"}],
            temperature=0.1, 
            stream=True,
            timeout=OPENAI_REQUEST_TIMEOUT_SECONDS
        )
        explanation_chunks = [chunk.choices[0].delta.content for chunk in response if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content]
        explanation = ''.join(explanation_chunks).strip()
        return explanation

    except APITimeoutError as e:
        if retry_count < DECOMPOSITION_MAX_RETRIES -1: 
            should_stop_in_retry = False
            try: 
                should_stop_in_retry = st.session_state.get('decomposition_should_stop', False) or \
                                   st.session_state.get('validator_should_stop', False)
            except AttributeError: pass
            if should_stop_in_retry: return "处理在重试等待期间被用户停止 (翻译拆解超时)"
            
            time.sleep(1 + retry_count) # Simple incremental backoff
            return explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count + 1)
        else: 
            error_msg = f"翻译拆解错误: API调用超时 (已重试 {DECOMPOSITION_MAX_RETRIES} 次) - {str(e)[:100]}"
            print(f"[API_ERROR_LOG] Decomposer: {error_msg}")
            return error_msg

    except APIStatusError as e:
        status_code = e.status_code
        error_detail = f"HTTP {status_code}: {e.message}"
        # Similar specific error handling as literal_translator
        if status_code == 401: error_detail = f"HTTP {status_code} (认证失败): {e.message}"
        elif status_code == 429:
            error_detail = f"HTTP {status_code} (请求超限): {e.message}"
            if retry_count < DECOMPOSITION_MAX_RETRIES -1:
                should_stop_in_retry = False # Copied from above
                try: 
                    should_stop_in_retry = st.session_state.get('decomposition_should_stop', False) or \
                                    st.session_state.get('validator_should_stop', False)
                except AttributeError: pass
                if should_stop_in_retry: return "处理在重试等待期间被用户停止 (翻译拆解429错误)"
                time.sleep(1 + retry_count) 
                return explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count + 1)
        elif status_code >= 500: error_detail = f"HTTP {status_code} (服务器错误): {e.message}"
        
        error_msg = f"翻译拆解错误: API调用失败 ({error_detail[:150]}) (已重试 {retry_count} 次)"
        print(f"[API_ERROR_LOG] Decomposer: {error_msg}")
        return error_msg

    except Exception as e:
        if retry_count < DECOMPOSITION_MAX_RETRIES -1: 
            should_stop = False
            try: 
                should_stop = st.session_state.get('decomposition_should_stop', False) or \
                              st.session_state.get('validator_should_stop', False) 
            except AttributeError: pass
            if should_stop: return "处理被用户停止 (翻译拆解模块常规错误重试前)"
                
            time.sleep(1 + retry_count)
            return explain_translation_text_decomposition_adapted(api_client, model_name, translation_text, retry_count + 1)
        else: 
            error_msg = f"翻译拆解错误: API调用失败 (已重试 {DECOMPOSITION_MAX_RETRIES} 次) - {str(e)[:100]}"
            print(f"[UNEXPECTED_ERROR_LOG] Decomposer: {error_msg}")
            return error_msg
